#! /usr/bin/env bash

# If a command returns a non-zero value (meaning failure), the program exits.
set -e

TOP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
source "${TOP_DIR}/scripts/apollo.bashrc"

ARCH="$(uname -m)"
SUPPORTED_ARCHS=" x86_64 aarch64 "
APOLLO_VERSION="@non-git"
APOLLO_ENV=""

#delete the unnecessary folders
echo "Remove dev......"
rm -rf dev
echo "Remove dumps......"
rm -rf dumps
echo "Remove distdir folder......"
rm -rf .cache/distdir
echo "Done"
echo "Remove repos folder......"
rm -rf .cache/bazel/cache/repos
echo "Done"
# echo "Remove build folder......"
# rm -rf .cache/build
echo "Remove bazel/install folder"
rm -rf .cache/bazel/install

rm -rf .git
rm -rf docs
rm -rf hd_map
rm -rf models
rm -rf vehicle_config
rm -rf modules/perception/data/*
rm -rf modules/perception/common/inference/inference_test_data
rm -rf modules/perception/inference/inference_test_data
rm -rf modules/prediction/evaluator/model_manager/model/
rm -rf modules/localization/msf
rm -rf modules/map/data
rm -rf data/log/*
rm -rf data/bag/*
rm -rf data/core/*
rm -rf data/gpsbin
rm -rf data/map_visual
# ln -s /home/<USER>/firmware/robosense_sdk_orin robosense_sdk_orin
echo "Done"
cd $(pwd)

#set the relative path
current_path=$(readlink -f .)
echo "current-path:"$current_path
bazel_out_path=$(readlink -f bazel-out)
bazel_test_path=$(readlink -f bazel-testlogs)
bazel_bin_path=$(readlink -f bazel-bin)
bazel_java_log=$(readlink -f bazel-apollo)

#remove the outer external folder
echo "bazel-out-path:"$bazel_out_path
cd $bazel_out_path
rm -rf k8-opt/bin/external/local_config_cuda
cd  ../../..
# echo "Remove outer external folders......"
cd external
# find ./ -maxdepth 1 ! -name "com_google_protobuf" ! -name "." |xargs rm -rf

echo "Done"
cd ..
echo "Remove bazel-install folder......"
rm -rf install
echo "Done"

#remove the testlog files
echo "bazel-test-path:"$bazel_test_path
cd $bazel_test_path
echo "Remove test log files......"
find . -name  test.log | xargs rm -rf
echo "Done"

#remove the object files
echo "bazel-java-log-path:"$bazel_java_log
cd $bazel_java_log
cd ../../
echo "Remove java log folder......"
rm -rf java.log*
echo "Done"

#remove the object files
echo "bazel-bin-path:"$bazel_bin_path
cd $bazel_bin_path
echo "Remove objs folder......"
find . -name  _objs | xargs rm -rf
# rm -rf external
# ln -s /home/<USER>/firmware/external external

# rm -rf modules/audio
# ln -s /home/<USER>/firmware/audio modules/audio

# rm -rf modules/tools
# ln -s /home/<USER>/firmware/tools modules/tools
echo "Done"

#remove the staic lib files
echo "Remove static lib files......"
find . -name  *.a | xargs rm -rf
echo "Done"

#remove the staic lib files
echo "Remove exampls files......"
find . -name  *example* | xargs rm -rf
echo "Done"

#remove the test files
echo "Remove test files......"
find ./cyber -name  *test* | xargs rm -rf
find ./modules -name  *test* | xargs rm -rf
find ./modules -name  *.lo | xargs rm -rf
rm -rf modules/perception/testdata
echo "Done"

#remove the sample files
:<<!
echo "Remove sample files......"
find . -name  *sample* | xargs rm -rf
echo "Done"
!

#remove the python folder in cyber
:<<!
echo "Remove cyber/python filders......"
rm -rf cyber/python
echo "Done"
!

#remove the io binary files in cyber
echo "Remove cyber/io/xxx filders......"
find ./cyber/io -name  *tcp* | xargs rm -rf
find ./cyber/io -name  *udp* | xargs rm -rf
echo "Done"

cd ${TOP_DIR}
cd .cache
find ./ -name *.obj | xargs rm -rf
cd ${TOP_DIR}
echo "Remove code resource"
rm -rf modules/prediction/testdata
find ./ -name *.c | xargs rm -rf
find ./ -name *.cc | xargs rm -rf
find ./ -name *.cpp | xargs rm -rf
find ./ -name *.h | xargs rm -rf
find ./ -name *.hpp | xargs rm -rf
mv README.md README
find ./ -name "*.md" | xargs rm -rf
find ./ -name "*clang*" | xargs rm -rf
find ./ -name "*LINT*" | xargs rm -rf
find ./ -name "*editorconfig" | xargs rm -rf
find ./ -name .gitignore | xargs rm -rf
find ./ -name LICENSE | xargs rm -rf
find ./ -name .prettierignore | xargs rm -rf
find ./ -name .prettierrc.json | xargs rm -rf
find ./ -name readthedocs.yml | xargs rm -rf
find ./ -name tox.ini | xargs rm -rf
find ./ -name *.o | xargs rm -rf
find ./ -name *.a | xargs rm -rf
find ./ -name CMakeLists.txt | xargs rm -rf
find ./third_party -name *.so | grep x86_64 | xargs rm -rf
find ./third_party -name *.so | grep teb | xargs rm -rf
find ./ -name *.proto | xargs rm -rf
find ./ -name *.MD | xargs rm -rf
find ./ -name *.BUILD | xargs rm -rf
find ./ -name workspace.bzl | xargs rm -rf
find ./ -name BUILD.* | xargs rm -rf
find ./ -name "*.bzl.*" | xargs rm -rf
find ./ -name *.bzl | xargs rm -rf
find ./ -name *.d | xargs rm -rf
find ./ -name _tmp | xargs rm -rf
find ./ -name "*.params" | xargs rm -rf
find ./ -name .gitlab | xargs rm -rf
mv README README.md
rm -rf scripts/ci
rm -rf auto_building.sh
rm -rf .bazelrc
rm -rf .gitlab-ci.yml
rm -rf .apollo.bazelrc
rm -rf apollo.doxygen
rm -rf apollo.sh
rm -rf WORKSPACE
rm -rf .gitlab
rm -rf .github
rm -rf docker
echo "Done"
echo $(pwd)

