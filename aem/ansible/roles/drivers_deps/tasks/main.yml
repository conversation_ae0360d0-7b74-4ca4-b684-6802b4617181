---
# tasks file for ansible/roles/drivers_deps
#- name: Install opencv
#  become: true
#  ansible.builtin.shell:
#    cmd: /opt/apollo/installers/install_opencv.sh
#    executable: /bin/bash
#  args: []
#  environment:
#    INSTALL_ATOM: "opencv-4.4.0"
#    INSTALL_PREFIX: "/opt/apollo/sysroot"

#- name: Install adv_plat
#  become: true
#  ansible.builtin.shell:
#    cmd: /opt/apollo/installers/install_adv_plat.sh
#    executable: /bin/bash
#  args: []
#  environment:
#    INSTALL_ATOM: "adv_plat-3.0.1"
#    INSTALL_PREFIX: "/opt/apollo/pkgs/adv_plat"

#- name: Install proj
#  become: true
#  ansible.builtin.shell:
#    cmd: /opt/apollo/installers/install_proj.sh
#    executable: /bin/bash
#  args: []
#  environment:
#    INSTALL_ATOM: "proj-7.1.0"
#    INSTALL_PREFIX: "/opt/apollo/sysroot"

- name: Install pyaudio portaudio19-dev
  become: true
  ansible.builtin.apt:
    name:
      - python3-pyaudio
      - portaudio19-dev
    state: latest
    update_cache: false

- name: Install libpacp
  become: true
  ansible.builtin.apt:
    name:
      - libpcap-dev
    state: latest
    update_cache: false

#- name: Install rsdriver
#  become: true
#  ansible.builtin.apt:
#    name:
#      - rsdriver
#    state: latest
#    update_cache: false
