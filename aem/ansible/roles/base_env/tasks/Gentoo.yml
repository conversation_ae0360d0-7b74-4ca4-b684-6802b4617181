---

- name: Install Basic Packages
  community.general.portage:
    package:
      - sys-devel/bc
      - app-misc/ca-certificates
      - net-misc/curl
      - sys-apps/file
      - sys-apps/gawk
      - dev-vcs/git
      - app-crypt/gnupg
      - sys-apps/less
      - sys-apps/lsb-release
      - sys-process/lsof
      - net-misc/rsync
      - sys-apps/sed
      - app-admin/sudo
      - app-text/tree
      - app-arch/unzip
      - app-editors/vim
      - net-misc/wget
      - app-arch/xz-utils
      - app-arch/zip
    state: present
  when: "'portage' in ansible_facts.packages"
