--- VTK-8.2.0/Rendering/FreeType/vtkFreeTypeTools.cxx.orig	2019-01-30 18:15:13.000000000 +0100
+++ VTK-8.2.0/Rendering/FreeType/vtkFreeTypeTools.cxx	2020-10-17 00:21:55.153442255 +0200
@@ -387,7 +387,7 @@ FTC_CMapCache* vtkFreeTypeTools::GetCMap
 }
 
 //----------------------------------------------------------------------------
-FT_CALLBACK_DEF(FT_Error)
+extern "C" FT_Error
 vtkFreeTypeToolsFaceRequester(FTC_FaceID face_id,
                               FT_Library lib,
                               FT_Pointer request_data,
--- VTK-8.2.0/Rendering/FreeTypeFontConfig/vtkFontConfigFreeTypeTools.cxx.orig	2019-01-30 18:15:13.000000000 +0100
+++ VTK-8.2.0/Rendering/FreeTypeFontConfig/vtkFontConfigFreeTypeTools.cxx	2020-10-17 00:38:23.457937944 +0200
@@ -26,10 +26,8 @@
 
 vtkStandardNewMacro(vtkFontConfigFreeTypeTools)
 
-namespace
-{
 // The FreeType face requester callback:
-FT_CALLBACK_DEF(FT_Error)
+extern "C" FT_Error
 vtkFontConfigFreeTypeToolsFaceRequester(FTC_FaceID face_id,
                                         FT_Library lib,
                                         FT_Pointer request_data,
@@ -75,7 +73,6 @@ vtkFontConfigFreeTypeToolsFaceRequester(
 
   return static_cast<FT_Error>(0);
 }
-} // end anon namespace
 
 void vtkFontConfigFreeTypeTools::PrintSelf(ostream &os, vtkIndent indent)
 {
