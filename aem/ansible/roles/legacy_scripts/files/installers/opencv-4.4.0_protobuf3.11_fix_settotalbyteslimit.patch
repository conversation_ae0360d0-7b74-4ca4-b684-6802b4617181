diff --git a/modules/dnn/src/caffe/caffe_io.cpp b/modules/dnn/src/caffe/caffe_io.cpp
index 2fc4d84f46..a6b73006d8 100644
--- a/modules/dnn/src/caffe/caffe_io.cpp
+++ b/modules/dnn/src/caffe/caffe_io.cpp
@@ -1111,7 +1111,11 @@ static const int kProtoReadBytesLimit = INT_MAX;  // Max size of 2 GB minus 1 by
 
 bool ReadProtoFromBinary(ZeroCopyInputStream* input, Message *proto) {
     CodedInputStream coded_input(input);
+#if GOOGLE_PROTOBUF_VERSION >= 3002000
+    coded_input.SetTotalBytesLimit(kProtoReadBytesLimit);
+#else
     coded_input.SetTotalBytesLimit(kProtoReadBytesLimit, 536870912);
+#endif
 
     return proto->ParseFromCodedStream(&coded_input);
 }
