# cyber

## 功能介绍

Apollo Cyber RT 是专为自动驾驶场景设计的开源高性能运行时框架。它基于集中式计算模型，针对自动驾驶中的高并发、低延迟和高吞
吐量进行了大幅优化。

在过去几年的自动驾驶技术发展中，我们从之前的 Apollo 经验中学到了很多。行业在发展，阿波罗也在发展。展望未来，Apollo 已经
从开发走向产品化，随着在现实世界中的批量部署，我们看到了对最高级别的稳健性和性能的需求。因此，我们花了数年时间打造并完善
Apollo Cyber RT，以满足自动驾驶解决方案的要求。

使用 Apollo Cyber RT 的主要优势：

- 加速开发
  - 明确定义的任务界面与数据融合
  - 一系列开发工具
  - 大量传感器驱动程序
- 简化部署
  - 高效的自适应信息通信
  - 可配置的用户级调度程序，具有资源感知功能
  - 可移植，依赖性更低
- 为自己的自动驾驶汽车赋能
  - 默认的开源运行时框架
  - 专为自动驾驶设计的构件
  - 即插即用您自己的 AD 系统

### 相关文档

@subpage md_cyber_2docs_2cyber\_\_quick\_\_start\_\_cn "Apollo Cyber RT 快速开始"

@subpage md_cyber_2docs_2cyber\_\_developer\_\_tools "Apollo Cyber RT 开发者工具"

@subpage md_cyber_2docs_2cyber\_\_api\_\_for\_\_developers "Apollo Cyber RT API"

@subpage md_cyber_2docs_2cyber\_\_python\_\_api\_\_cn "Apollo Cyber RT Python API"

@subpage md_cyber_2docs_2cyber\_\_faqs "Apollo Cyber RT FAQs"

@subpage md_cyber_2docs_2cyber\_\_terms "Apollo Cyber RT 术语"

@subpage md_cyber_2docs_2cyber\_\_scheduler\_\_cn "Apollo Cyber RT 调度策略"
