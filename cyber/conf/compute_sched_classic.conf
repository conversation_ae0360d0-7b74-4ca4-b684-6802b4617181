scheduler_conf {
  policy: "classic"
  classic_conf {
    groups: [
      {
        name: "compute"
        processor_num: 16
        affinity: "range"
        cpuset: "0-7,16-23"
        processor_policy: "SCHED_OTHER"
        processor_prio: 0
        tasks: [
          {
            name: "velodyne_16_front_center_convert"
            prio: 10
          },
          {
            name: "velodyne_16_rear_left_convert"
            prio: 10
          },
          {
            name: "velodyne_16_rear_right_convert"
            prio: 10
          },
          {
            name: "velodyne_fusion"
            prio: 11
          },
          {
            name: "velodyne16_fusion_compensator"
            prio: 11
          },
          {
            name: "Velodyne16Detection"
            prio: 11
          },
          {
            name: "velodyne_128_convert"
            prio: 11
          },
          {
            name: "velodyne128_compensator"
            prio: 12
          },
          {
            name: "Velodyne128Detection"
            prio: 13
          },
          {
            name: "RecognitionComponent"
            prio: 14
          },
          {
            name: "SensorFusion"
            prio: 15
          },
          {
            name: "prediction"
            prio: 16
          },
          {
            name: "planning"
            prio: 17
          },
          {
            name: "planning_/apollo/perception/traffic_light"
            prio: 17
          },
          {
            name: "routing"
            prio: 19
          },
          {
            name: "planning_/apollo/routing_response"
            prio: 19
          },
          {
            name: "msf_localization"
            prio: 20
          },
          {
            name: "rtk_localization"
            prio: 20
          },
          {
            name: "msf_localization_/apollo/sensor/lidar64/compensator/PointCloud2"
            prio: 20
          }
        ]
      },
      {
        name: "compute_camera"
        processor_num: 16
        affinity: "range"
        cpuset: "8-15,24-31"
        processor_policy: "SCHED_OTHER"
        processor_prio: 0
        tasks: [
          {
            name: "camera_front_6mm_compress"
            prio: 0
          },
          {
            name: "camera_front_12mm_compress"
            prio: 0
          },
          {
            name: "camera_left_fisheye_compress"
            prio: 0
          },
          {
            name: "camera_right_fisheye_compress"
            prio: 0
          },
          {
            name: "camera_rear_6mm_compress"
            prio: 0
          }
        ]
      }
    ]
  }
}
