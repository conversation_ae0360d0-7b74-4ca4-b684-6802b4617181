scheduler_conf {
  policy: "classic"
  process_level_cpuset: "0-7"
  threads: [
      {
          name: "shm_disp"
          cpuset: "0-7"
          policy: "SCHED_FIFO"
          prio:22
      }
  ]
  classic_conf {
    groups: [
      {
        name: "tcp_transport_sched"
        processor_num: 16
        affinity: "range"
        cpuset: "0-7"
        processor_policy: "SCHED_FIFO"
        processor_prio: 20
        tasks: [
        
        ]
      }
    ]
  }
}
