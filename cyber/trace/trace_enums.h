/******************************************************************************
 * Copyright 2023 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#pragma once
namespace apollo {
namespace trace {

enum TaskType {
  TASK_<PERSON>INITIALIZED,
  SCH<PERSON><PERSON>LER_SCHED,
  SCHEDULER_PROC,
  TIMER_SCHED,
  TIMER_PROC,
  SHM_NOTIFY,
  SHM_DISPATCH,
  TCP_TRANSPORT,
};

enum TimestampType { TIMESTAMP_UNINITIALIZED, BEGIN, END };

}  // namespace trace
}  // namespace apollo
