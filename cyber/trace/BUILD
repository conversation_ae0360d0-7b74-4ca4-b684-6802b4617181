load("//tools:cpplint.bzl", "cpplint")
load("//tools:apollo_package.bzl", "apollo_cc_library", "apollo_package", "apollo_cc_test")

package(default_visibility = ["//visibility:public"])

apollo_cc_library(
    name = "trace",
    hdrs = ["trace.h"],
    deps = [
        ":writer",
        ":common"
    ],
)

apollo_cc_library(
    name = "writer",
    hdrs = ["writer.h"],
    deps = [
        "//third_party/concurrent_queue",
        ":common"
    ],
    linkopts = ["-lstdc++fs"]
)

apollo_cc_library(
    name = "common",
    hdrs = ["common.h"],
    srcs = ["common.cc"],
    deps = [
        "//cyber/base:cyber_base",
        "//cyber/common:cyber_common",
        ":trace_enums"
    ]
)

apollo_cc_library(
    name = "trace_enums",
    hdrs = ["trace_enums.h"],
)

apollo_package()
cpplint()
