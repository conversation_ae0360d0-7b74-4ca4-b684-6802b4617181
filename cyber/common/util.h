/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#ifndef CYBER_COMMON_UTIL_H_
#define CYBER_COMMON_UTIL_H_

#include <string>
#include <type_traits>

namespace apollo {
namespace cyber {
namespace common {

inline std::size_t Hash(const std::string& key) {
  return std::hash<std::string>{}(key);
}

inline uint64_t ConcatNumber(uint64_t num1, uint64_t num2, int shift_bit = 32) {
  return (num1 << shift_bit) | (num2 & ((1ULL << shift_bit) - 1));
}

template <typename Enum>
auto ToInt(Enum const value) -> typename std::underlying_type<Enum>::type {
  return static_cast<typename std::underlying_type<Enum>::type>(value);
}

}  // namespace common
}  // namespace cyber
}  // namespace apollo

#endif  // CYBER_COMMON_UTIL_H_
