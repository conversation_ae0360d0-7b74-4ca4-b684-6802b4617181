/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#ifndef CYBER_TRANSPORT_RECEIVER_SHM_RECEIVER_H_
#define CYBER_TRANSPORT_RECEIVER_SHM_RECEIVER_H_

#include <functional>
#include <unordered_map>

#include "cyber/common/log.h"
#include "cyber/transport/dispatcher/shm_dispatcher.h"
#include "cyber/transport/receiver/receiver.h"
#include "cyber/transport/shm/protobuf_arena_manager.h"

namespace apollo {
namespace cyber {
namespace transport {

template <typename M>
class ShmReceiver : public Receiver<M> {
 public:
  ShmReceiver(const RoleAttributes& attr,
              const typename Receiver<M>::MessageListener& msg_listener);
  virtual ~ShmReceiver();

  void Enable() override;
  void Disable() override;

  void Enable(const RoleAttributes& opposite_attr) override;
  void Disable(const RoleAttributes& opposite_attr) override;

 private:
  std::unordered_map<uint64_t, RoleAttributes> transmitters_;
};

template <typename M>
ShmReceiver<M>::ShmReceiver(
    const RoleAttributes& attr,
    const typename Receiver<M>::MessageListener& msg_listener)
    : Receiver<M>(attr, msg_listener) {
  ShmDispatcher::Instance();
  transmitters_.clear();
}

template <typename M>
ShmReceiver<M>::~ShmReceiver() {
  Disable();
  transmitters_.clear();
}

template <typename M>
void ShmReceiver<M>::Enable() {
  if (this->enabled_) {
    return;
  }

  if (cyber::common::GlobalData::Instance()->IsChannelEnableArenaShm(
          this->attr_.channel_id()) && message::MessageType<M>() != \
            message::MessageType<message::RawMessage>()) {
    auto arena_manager = ProtobufArenaManager::Instance();
    if (!arena_manager->Enable() ||
        !arena_manager->EnableSegment(this->attr_.channel_id())) {
      AERROR << "arena manager enable failed.";
      return;
    }
  }

  ShmDispatcher::Instance(false)->AddListener<M>(
      this->attr_, std::bind(&ShmReceiver<M>::OnNewMessage, this,
                             std::placeholders::_1, std::placeholders::_2));
  this->enabled_ = true;
}

template <typename M>
void ShmReceiver<M>::Disable() {
  if (!this->enabled_) {
    return;
  }

  ShmDispatcher::Instance(false)->RemoveListener<M>(this->attr_);
  this->enabled_ = false;
}

template <typename M>
void ShmReceiver<M>::Enable(const RoleAttributes& opposite_attr) {
  if (cyber::common::GlobalData::Instance()->IsChannelEnableArenaShm(
          this->attr_.channel_id()) && message::MessageType<M>() != \
            message::MessageType<message::RawMessage>()) {
    auto arena_manager = ProtobufArenaManager::Instance();
    if (!arena_manager->Enable() ||
        !arena_manager->EnableSegment(this->attr_.channel_id())) {
      AERROR << "arena manager enable failed.";
      return;
    }
  }
  
  if (transmitters_.count(opposite_attr.id()) > 0) {
    return;
  }
  transmitters_[opposite_attr.id()] = opposite_attr;

  if (this->attr_.tcp_flag()) {
    ShmDispatcher::Instance(false)->AddListenerTrans(this->attr_,
                                                     opposite_attr);
  } else {
    ShmDispatcher::Instance(false)->AddListener<M>(
        this->attr_, opposite_attr,
        std::bind(&ShmReceiver<M>::OnNewMessage, this, std::placeholders::_1,
                  std::placeholders::_2));
  }
}

template <typename M>
void ShmReceiver<M>::Disable(const RoleAttributes& opposite_attr) {
  ShmDispatcher::Instance(false)->RemoveListener<M>(this->attr_, opposite_attr);
}

}  // namespace transport
}  // namespace cyber
}  // namespace apollo

#endif  // CYBER_TRANSPORT_RECEIVER_SHM_RECEIVER_H_
