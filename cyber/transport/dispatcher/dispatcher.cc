/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "cyber/transport/dispatcher/dispatcher.h"

namespace apollo {
namespace cyber {
namespace transport {

Dispatcher::Dispatcher() : is_shutdown_(false) {}

Dispatcher::~Dispatcher() { Shutdown(); }

void Dispatcher::Shutdown() {
  is_shutdown_.store(true);
  ADEBUG << "Shutdown";
}

bool Dispatcher::HasChannel(uint64_t channel_id) {
  return msg_listeners_.Has(channel_id);
}

}  // namespace transport
}  // namespace cyber
}  // namespace apollo
