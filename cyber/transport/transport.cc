/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "cyber/transport/transport.h"

#include "cyber/common/global_data.h"
#include "cyber/transport/dispatcher/rtps_dispatcher.h"

namespace apollo {
namespace cyber {
namespace transport {

Transport::Transport() {
  CreateParticipant();
  // Fix bugs for the singleton's resource release

  // To avoid core dumps when singleton objects finally release resources, all
  // singleton object initializations involving multithreaded management are
  // placed here.
  NotifierFactory::CreateNotifier();
  IntraDispatcher::Instance();
  ShmDispatcher::Instance();
  RtpsDispatcher::Instance()->SetParticipant(participant_);
}

Transport::~Transport() { Shutdown(); }

void Transport::Shutdown() {
  if (is_shutdown_.exchange(true)) {
    return;
  }
  // Fix bugs for the singleton's resource release

  // Use the CleanUp() function of the singleton class to safely free resources!
  NotifierFactory::CleanUp();
  IntraDispatcher::CleanUp();
  ShmDispatcher::CleanUp();
  RtpsDispatcher::CleanUp();

  if (participant_ != nullptr) {
    participant_->Shutdown();
    participant_ = nullptr;
  }
}

void Transport::CreateParticipant() {
  std::string participant_name =
      common::GlobalData::Instance()->HostName() + "+" +
      std::to_string(common::GlobalData::Instance()->ProcessId());
  participant_ = std::make_shared<
      Participant>(participant_name, 11512);
  if (!participant_->Init()) {
    AERROR << "Transport inner participant init failed!";
  }
}

}  // namespace transport
}  // namespace cyber
}  // namespace apollo
