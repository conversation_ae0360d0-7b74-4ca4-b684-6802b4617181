/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "cyber/transport/shm/notifier_factory.h"

#include "cyber/common/global_data.h"
#include "cyber/common/log.h"
#include "cyber/transport/shm/condition_notifier.h"
#include "cyber/transport/shm/multicast_notifier.h"

namespace apollo {
namespace cyber {
namespace transport {

using common::GlobalData;

std::string NotifierFactory::notifier_type_(ConditionNotifier::Type());

NotifierPtr NotifierFactory::CreateNotifier() {
  auto& g_conf = GlobalData::Instance()->Config();
  if (g_conf.has_transport_conf() && g_conf.transport_conf().has_shm_conf() &&
      g_conf.transport_conf().shm_conf().has_notifier_type()) {
    notifier_type_ = g_conf.transport_conf().shm_conf().notifier_type();
  }

  ADEBUG << "notifier type: " << notifier_type_;

  if (notifier_type_ == MulticastNotifier::Type()) {
    return CreateMulticastNotifier();
  } else if (notifier_type_ == ConditionNotifier::Type()) {
    return CreateConditionNotifier();
  }

  AINFO << "unknown notifier, we use default notifier: " << notifier_type_;
  return CreateConditionNotifier();
}

void NotifierFactory::CleanUp() {
  if (notifier_type_ == MulticastNotifier::Type()) {
    return MulticastNotifier::CleanUp();
  } else if (notifier_type_ == ConditionNotifier::Type()) {
    return ConditionNotifier::CleanUp();
  }

  AINFO << "unknown notifier, we use default notifier: " << notifier_type_;
  return ConditionNotifier::CleanUp();
}

NotifierPtr NotifierFactory::CreateConditionNotifier() {
  return ConditionNotifier::Instance();
}

NotifierPtr NotifierFactory::CreateMulticastNotifier() {
  return MulticastNotifier::Instance();
}

}  // namespace transport
}  // namespace cyber
}  // namespace apollo
