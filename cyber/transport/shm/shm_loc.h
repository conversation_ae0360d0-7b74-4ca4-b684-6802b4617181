
#ifndef _SHM_LOC_H_
#define _SHM_LOC_H_

#include <iostream>
#include <memory>
#include <string>

#include "cyber/common/log.h"

namespace apollo {
namespace cyber {
namespace transport {

/*
Large block data transmission delay（memory copying, proto serialization).
So directly operate shared memory, including get, attach, detach, delete.

In pursuit of performance:
1.The data structure needs to be customized and cannot be in proto format.
2.There is no lock, so a memory can only have one reader and one writer.
3.When init, the reader and writer need to specify the same shm_key.
*/

constexpr uint8_t kDisplayInterval = 100;
#define IPC_MODE IPC_CREAT | 0666
enum ResultCode {
  RS_SUCCESS = 0,
  RS_COMMON_ERROR,
  RS_ALLOC,
  RS_NO_SHM_FOR_RW,
};

class ShmBase {
 public:
  ShmBase();
  virtual ~ShmBase();
  int32_t Init(int32_t key, uint32_t size);
  int32_t GetShmId(int32_t flag);
  char *GetAddr();
  char *GetShm(int32_t flag);
  int32_t DetachShm(char **addr);
  int32_t DeleteShm();

 private:
  int32_t shm_id_ = -1;
  int32_t shm_key_ = -1;
  uint32_t shm_size_ = 0;
};

struct ShmControl {
  int32_t init_flag = 0;
  int32_t dir = 0;
  uint32_t startPos = 0;
  uint32_t endPos = 0;
  uint32_t readPos = 0;
  uint32_t writePos = 0;

  void Display(const char info[]) {
    ADEBUG << "ShmControl:info=" << info << ",dir=" << dir
           << ",startPos=" << startPos << ",endPos=" << endPos
           << ",readPos=" << readPos << ",writePos=" << writePos;
  }
};

struct ShmDataHead {
  uint32_t flag = 0;
  uint32_t size = 0;
};

class ShmOperationRecord {
 public:
  ShmOperationRecord() = default;
  ~ShmOperationRecord() = default;

  void AddOperationRecord(bool res_flag) {
    if (res_flag) {
      ++success_cnt;
    } else {
      ++fail_cnt;
    }
    ++total_cnt;
    if (0 == total_cnt % kDisplayInterval) {
      Display();
      success_cnt = 0;
      fail_cnt = 0;
    }
  }

  void Display() {
    AINFO << "ShmOperationRecord info. total_cnt:" << total_cnt
          << ", success_cnt:" << success_cnt << ", fail_cnt:" << fail_cnt;
  }

 private:
  uint32_t total_cnt = 0;
  uint32_t success_cnt = 0;
  uint32_t fail_cnt = 0;
};

class ShmInterface {
 public:
  ShmInterface();
  virtual ~ShmInterface();

  int32_t Init(int32_t key, uint32_t size, bool force = false);
  int32_t Init(std::string key_str, uint32_t size, bool force = false);

  int32_t ReadData(ShmDataHead *head, void *content);
  int32_t WriteData(ShmDataHead *head, const void *content);

  template <typename MessageT>
  bool WriteData(const std::shared_ptr<MessageT> &msg_ptr, uint32_t msg_size) {
    const auto &msg = *msg_ptr;
    ShmDataHead data_head;
    data_head.size = msg_size;
    int32_t ret = WriteData(&data_head, reinterpret_cast<const void *>(&msg));
    if (RS_SUCCESS != ret) {
      if (RS_NO_SHM_FOR_RW == ret) {
        ADEBUG << "Writing sharedmem message failed: RS_NO_SHM_FOR_RW";
      } else {
        AERROR << "Writing sharedmem message failed: " << ret;
      }
      return false;
    } else {
      ADEBUG << "Writing sharedmem message success: ";
    }
    return true;
  }

  template <typename MessageT>
  std::shared_ptr<MessageT> ReadData() {
    auto msg_ptr = std::make_shared<MessageT>();
    auto &msg = *msg_ptr;
    static ShmDataHead head;
    int32_t ret = ReadData(&head, reinterpret_cast<void *>(&msg));
    if (RS_SUCCESS != ret) {
      if (RS_NO_SHM_FOR_RW == ret) {
        ADEBUG << "Reading sharedmem message failed: RS_NO_SHM_FOR_RW";
      } else {
        AERROR << "Reading sharedmem message failed: " << ret;
      }
      return nullptr;
    } else {
      ADEBUG << "Reading sharedmem message success: "
             << "head size:" << head.size;
    }
    return msg_ptr;
  }

 private:
  bool CheckShmWriteability(ShmDataHead *head);
  bool CheckShmReadability();

 private:
  ShmBase *shmbase_ = nullptr;
  char *addr_ = nullptr;
  ShmControl *shm_control_ = nullptr;
  ShmOperationRecord shm_operation_record_;
};
}  // namespace transport
}  // namespace cyber
}  // namespace apollo

#endif  // _SHM_LOC_H_
