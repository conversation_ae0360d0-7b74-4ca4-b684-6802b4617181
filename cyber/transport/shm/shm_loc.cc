#include "cyber/transport/shm/shm_loc.h"

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/types.h>

#include "cyber/common/global_data.h"

namespace apollo {
namespace cyber {
namespace transport {
namespace {
constexpr uint32_t SHM_UNIT_M = 1024 * 1024;
}  // namespace

ShmBase::ShmBase() {}
ShmBase::~ShmBase() {}

int32_t ShmBase::Init(int32_t key, uint32_t size) {
  shm_key_ = key;
  shm_size_ = size * SHM_UNIT_M;
  return RS_SUCCESS;
}

int32_t ShmBase::GetShmId(int32_t flag) {
  if (shm_id_ >= 0) {
    return shm_id_;
  }

  shm_id_ = shmget(shm_key_, shm_size_, flag);
  if (shm_id_ < 0) {
    AERROR << "shmget error,errno:" << errno << ", err_msg:" << strerror(errno);
    return RS_COMMON_ERROR;
  }

  return shm_id_;
}

char *ShmBase::GetAddr() {
  if (shm_id_ < 0) {
    AERROR << "shm_id_ is invaild.";
    return nullptr;
  }

  char *shm_addr = reinterpret_cast<char *>(shmat(shm_id_, nullptr, 0));
  if (reinterpret_cast<char *>(-1) == shm_addr) {
    AERROR << "shmat error,errno:" << errno << ", err_msg:" << strerror(errno);
    //  shmctl(shm_id_, IPC_RMID, 0);
    return nullptr;
  }

  return shm_addr;
}

char *ShmBase::GetShm(int32_t flag) {
  if (GetShmId(flag) < 0) {
    AERROR << "GetShmId is error.";
    return nullptr;
  }

  return GetAddr();
}

int32_t ShmBase::DetachShm(char **addr) {
  int32_t ret = RS_SUCCESS;
  if (nullptr == *addr) {
    return RS_SUCCESS;
  }

  ret = shmdt(*addr);
  if (0 != ret) {
    AERROR << "shmdt error,errno:" << errno << ", err_msg:" << strerror(errno);
    return ret;
  }
  *addr = nullptr;
  return ret;
}

int32_t ShmBase::DeleteShm() {
  if (shm_id_ < 0) {
    AERROR << "shm_id_ is invaild.";
    return RS_COMMON_ERROR;
  }

  int32_t ret = shmctl(shm_id_, IPC_RMID, nullptr);
  if (ret < 0) {
    AERROR << "shmctl error,errno:" << errno << ", err_msg:" << strerror(errno);
    return ret;
  }

  return ret;
}

ShmInterface::ShmInterface() {}

ShmInterface::~ShmInterface() {
  if (shmbase_ != nullptr) {
    shmbase_->DetachShm(&addr_);

    delete shmbase_;
    shmbase_ = nullptr;
  }
}

// size : shm size ,Unit is M byte
// force : writer is true, reader is flase
int32_t ShmInterface::Init(std::string key_str, uint32_t size, bool force) {
  auto key = std::hash<std::string>{}(key_str);
  AINFO << "key_str=" << key_str << ",key=" << key;
  return Init(key, size, force);
}

// size : shm size ,Unit is M byte
// force : writer is true, reader is flase
int32_t ShmInterface::Init(int32_t key, uint32_t size, bool force) {
  int32_t ret = RS_SUCCESS;
  if (nullptr == shmbase_) {
    shmbase_ = new ShmBase();
    if (nullptr == shmbase_) {
      AERROR << "alloc shmbase_ error,errno:" << errno
             << ", err_msg:" << strerror(errno);
      return RS_ALLOC;
    }
  }

  shmbase_->Init(key, size);

  addr_ = shmbase_->GetShm(IPC_MODE);
  if (nullptr == addr_) {
    AERROR << "exec GetShm failed!";
    return RS_COMMON_ERROR;
  }

  shm_control_ = reinterpret_cast<ShmControl *>(addr_);
  if (force || shm_control_->init_flag != 1) {
    shm_control_->startPos = sizeof(ShmControl);
    shm_control_->endPos = size * SHM_UNIT_M;
    shm_control_->readPos = shm_control_->startPos;
    shm_control_->writePos = shm_control_->startPos;
    shm_control_->init_flag = 1;
    shm_control_->dir = 1;
  }

  // ret = shmbase_->DetachShm(&addr_);
  // if (ret != RS_SUCCESS) {
  //   AERROR << "exec DetachShm failed!";
  //   return RS_COMMON_ERROR;
  // }
  return ret;
}

int32_t ShmInterface::ReadData(ShmDataHead *head, void *content) {
  int32_t ret = RS_SUCCESS;
  if (nullptr == addr_) {
    addr_ = shmbase_->GetShm(IPC_MODE);
  }
  if (nullptr == addr_) {
    AERROR << "shmbase_->GetShm failed!";
    return RS_COMMON_ERROR;
  }

  shm_control_ = reinterpret_cast<ShmControl *>(addr_);

  shm_control_->Display("ReadBefore");
  if (CheckShmReadability()) {
    memcpy(reinterpret_cast<char *>(head), addr_ + shm_control_->readPos,
           sizeof(ShmDataHead));
    memcpy(reinterpret_cast<char *>(content),
           addr_ + shm_control_->readPos + sizeof(ShmDataHead), head->size);
    memset(addr_ + shm_control_->readPos, 0,
             sizeof(ShmDataHead) + head->size);
    shm_control_->readPos += sizeof(ShmDataHead) + head->size;
  } else {
    ADEBUG << "exec CheckShmReadability failed!";
    ret = RS_NO_SHM_FOR_RW;
  }
  shm_control_->Display("ReadEnd");
  shm_operation_record_.AddOperationRecord(RS_SUCCESS == ret);
  // shmbase_->DetachShm(&addr_);

  return ret;
}

int32_t ShmInterface::WriteData(ShmDataHead *head, const void *content) {
  int32_t ret = RS_SUCCESS;
  if (nullptr == addr_) {
    addr_ = shmbase_->GetShm(IPC_MODE);
  }
  if (nullptr == addr_) {
    AERROR << "shmbase_->GetShm failed!";
    return RS_COMMON_ERROR;
  }

  shm_control_ = reinterpret_cast<ShmControl *>(addr_);

  shm_control_->Display("WriteBefore");
  if (CheckShmWriteability(head)) {
    memcpy(addr_ + shm_control_->writePos, reinterpret_cast<char *>(head),
           sizeof(ShmDataHead));
    memcpy(addr_ + shm_control_->writePos + sizeof(ShmDataHead),
           reinterpret_cast<const char *>(content), head->size);
    shm_control_->writePos += sizeof(ShmDataHead) + head->size;
  } else {
    ADEBUG << "exec CheckShmWriteability failed!";
    ret = RS_NO_SHM_FOR_RW;
  }
  shm_control_->Display("WriteEnd");
  shm_operation_record_.AddOperationRecord(RS_SUCCESS == ret);
  // shmbase_->DetachShm(&addr_);

  return ret;
}

bool ShmInterface::CheckShmWriteability(ShmDataHead *head) {
  int32_t size = head->size + sizeof(ShmDataHead);
  if ((shm_control_->writePos > shm_control_->readPos) ||
      (shm_control_->dir > 0 &&
       shm_control_->writePos == shm_control_->readPos)) {
    if (shm_control_->writePos + size <= shm_control_->endPos) {
      return true;
    } else {
      shm_control_->writePos = shm_control_->startPos;
      shm_control_->dir *= -1;
    }
  }

  if (shm_control_->writePos < shm_control_->readPos) {
    if (shm_control_->writePos + size <= shm_control_->readPos) {
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
}

bool ShmInterface::CheckShmReadability() {
  if ((shm_control_->writePos < shm_control_->readPos) ||
      (shm_control_->dir < 0 &&
       shm_control_->writePos == shm_control_->readPos)) {
    if (shm_control_->readPos + sizeof(ShmDataHead) < shm_control_->endPos) {
      ShmDataHead *head =
          reinterpret_cast<ShmDataHead *>(addr_ + shm_control_->readPos);
      if (head->size > 0 &&
          shm_control_->readPos + sizeof(ShmDataHead) + head->size <=
              shm_control_->endPos) {
        return true;
      } else {
        shm_control_->readPos = shm_control_->startPos;
        shm_control_->dir *= -1;
      }
    } else {
      shm_control_->readPos = shm_control_->startPos;
      shm_control_->dir *= -1;
    }
  }

  if (shm_control_->writePos > shm_control_->readPos) {
    return true;
  } else {
    return false;
  }
}

}  // namespace transport
}  // namespace cyber
}  // namespace apollo
