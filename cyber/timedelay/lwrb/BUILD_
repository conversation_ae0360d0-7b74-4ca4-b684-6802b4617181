load("//tools:apollo_package.bzl", "apollo_cc_library", "apollo_package", "apollo_cc_test")
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_import")
load("//tools/platform:build_defs.bzl", "if_aarch64", "if_x86_64")
package(default_visibility = ["//visibility:public"])

cc_import(
    name = "lwrb_shared",
    shared_library = select({
        "@platforms//cpu:x86_64": "x86_64_lib/lib/liblwrb.so",
        "@platforms//cpu:aarch64": "aarch64_lib/lib/liblwrb.so",
    }),
)

apollo_cc_library(
    name = "lwrb",
    hdrs = glob(["include/**"]),
    includes = ["include"],
    deps = [":lwrb_shared"],
)

apollo_package()