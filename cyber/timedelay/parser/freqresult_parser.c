#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define FREQERESULT_FILENAME_TARGETPATH "freqresult.log"

int main(int argc, char **argv) {
  char buff[1024];

  snprintf(buff, sizeof(buff), "%s/%s", argv[1], argv[2]);
  FILE *delayfp = fopen(buff, "r");
  if (delayfp == NULL) {
    printf("fopen %s fail\n", buff);
    exit(-1);
  }

  snprintf(buff, sizeof(buff), "%s/%s", argv[1],
           FREQERESULT_FILENAME_TARGETPATH);
  FILE *freqfp = fopen(buff, "w+");
  if (freqfp == NULL) {
    printf("fopen %s fail\n", buff);
    exit(-1);
  }

  int linenumber = 0;
  while (1) {
    if (feof(delayfp)) {
      printf("delayfp file is end\n");
      exit(-1);
    }

    memset(buff, 0, sizeof(buff));
    fgets(buff, sizeof(buff), delayfp);
    linenumber++;
    long long lidar[5] = {0};
    long long fusion[4] = {0};
    long long intradomain[2] = {0};
    long long prediction[4] = {0};
    long long crossdomain[2] = {0};
    long long planning[4] = {0};
    long long controll[4] = {0};
    long long canbus[4] = {0};
    static long long lidarexposure = 0, lidarrecv = 0, lidarsend = 0;
    static long long fusionrecv = 0, fusionsend = 0;
    static long long intradomainrecv = 0;
    static long long predictionrecv = 0, predictionsend = 0;
    static long long crossdomainrecv = 0;
    static long long planningrecv = 0, planningsend = 0;
    static long long controllrecv = 0, controllsend = 0;
    static long long canbusrecv = 0, canbussend = 0;
    if (sscanf(buff,
               "%lld %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld "
               "%lld %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld "
               "%lld %lld %lld %lld %lld",
               &lidar[0], &lidar[1], &lidar[2], &lidar[3], &lidar[4],
               &fusion[0], &fusion[1], &fusion[2], &fusion[3], &intradomain[0],
               &intradomain[1], &prediction[0], &prediction[1], &prediction[2],
               &prediction[3], &crossdomain[0], &crossdomain[1], &planning[0],
               &planning[1], &planning[2], &planning[3], &controll[0],
               &controll[1], &controll[2], &controll[3], &canbus[0], &canbus[1],
               &canbus[2], &canbus[3]) != 29) {
      printf("sscanf error! buff:%s\n", buff);
      exit(-1);
    }

    if (lidarexposure == 0) {
      lidarexposure = lidar[0];
      lidarrecv = lidar[2];
      lidarsend = lidar[4];
      fusionrecv = fusion[1];
      fusionsend = fusion[3];
      intradomainrecv = intradomain[1];
      predictionrecv = prediction[1];
      predictionsend = prediction[3];
      crossdomainrecv = crossdomain[1];
      planningrecv = planning[1];
      planningsend = planning[3];
      controllrecv = controll[1];
      controllsend = controll[3];
      canbusrecv = canbus[1];
      canbussend = canbus[3];
      continue;
    }

    snprintf(
        buff, sizeof(buff),
        "%lld %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld "
        "%lld %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld %lld "
        "%lld %lld %lld %lld %lld %0.3f %0.3f %0.3f %0.3f %0.3f %0.3f %0.3f "
        "%0.3f %0.3f %0.3f %0.3f %0.3f %0.3f %0.3f %0.3f\n",
        lidar[0], lidar[1], lidar[2], lidar[3], lidar[4], fusion[0], fusion[1],
        fusion[2], fusion[3], intradomain[0], intradomain[1], prediction[0],
        prediction[1], prediction[2], prediction[3], crossdomain[0],
        crossdomain[1], planning[0], planning[1], planning[2], planning[3],
        controll[0], controll[1], controll[2], controll[3], canbus[0],
        canbus[1], canbus[2], canbus[3], (lidar[0] - lidarexposure) / 1000000.0,
        (lidar[2] - lidarrecv) / 1000000.0, (lidar[4] - lidarsend) / 1000000.0,
        (fusion[1] - fusionrecv) / 1000000.0,
        (fusion[3] - fusionsend) / 1000000.0,
        (prediction[1] - predictionrecv) / 1000000.0,
        (prediction[3] - predictionsend) / 1000000.0,
        (planning[1] - planningrecv) / 1000000.0,
        (planning[3] - planningsend) / 1000000.0,
        (controll[1] - controllrecv) / 1000000.0,
        (controll[3] - controllsend) / 1000000.0,
        (canbus[1] - canbusrecv) / 1000000.0,
        (canbus[3] - canbussend) / 1000000.0,
        (intradomain[1] - intradomainrecv) / 1000000.0,
        (crossdomain[1] - crossdomainrecv) / 1000000.0);
    lidarexposure = lidar[0];
    lidarrecv = lidar[2];
    lidarsend = lidar[4];
    fusionrecv = fusion[1];
    fusionsend = fusion[3];
    intradomainrecv = intradomain[1];
    predictionrecv = prediction[1];
    predictionsend = prediction[3];
    crossdomainrecv = crossdomain[1];
    planningrecv = planning[1];
    planningsend = planning[3];
    controllrecv = controll[1];
    controllsend = controll[3];
    canbusrecv = canbus[1];
    canbussend = canbus[3];
    fputs(buff, freqfp);
    fflush(freqfp);
  }

  fclose(delayfp);
  fclose(freqfp);
  return 0;
}