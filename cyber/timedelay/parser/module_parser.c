#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define TARGETPATH "target/"
#define CANBUS_FILENAME TARGETPATH "canbus.parser.log"
#define CONTROLL_FILENAME TARGETPATH "controll.parser.log"
#define PLANNING_FILENAME TARGETPATH "planning.parser.log"
#define PREDICTION_FILENAME TARGETPATH "prediction.parser.log"
#define FUSION_FILENAME TARGETPATH "/fusion.parser.log"
#define HDLIDAR_FILENAME TARGETPATH "/lidar.parser.log"
#define MONITOR_FUSION_FILENAME TARGETPATH "monitor_fusion.parser.log"
#define MONITOR_PREDICTION_FILENAME TARGETPATH "monitor_prediction.parser.log"

#define TRACE_CANBUSRECV "canbus_recv"
#define TRACE_CANBUSSEND "canbus_send"
#define TRACE_CONTROLLRECV "controll_recv"
#define TRACE_CONTROLLSEND "controll_send"
#define TRACE_PLANNINGRECV "planning_recv"
#define TRACE_PLANNINGSEND "planning_send"
#define TRACE_PREDICTIONRECV "prediction_recv"
#define TRACE_PREDICTIONSEND "prediction_send"
#define TRACE_FUSION_HDLIDAR "fusion_lidar_recv"
#define TRACE_FUSIONSEND "fusion_send"
#define TRACE_HDLIDARALGORRECV "lidar_algor_recv"
#define TRACE_HDLIDARALGORSEND "lidar_algor_send"
#define TRACE_MONITOR_FUSION "monitor_fusion"
#define TRACE_MONITOR_PREDICTION "monitor_prediction"

void module_parser_third(FILE *infp, FILE *oufp, const char *name) {
  char linebuff[512] = {0};
  char tempid[64] = {0};
  char tempseq[64] = {0};
  char temptime[64] = {0};

  while (!feof(infp)) {
    memset(linebuff, 0, sizeof(linebuff));
    fgets(linebuff, sizeof(linebuff), infp);
    if (sscanf(linebuff, "[%[^]]][%[^]]][%[^]]]", tempid, tempseq, temptime) !=
        3) {
      printf("sscanf %s error, tempid:%s,tempseq:%s,temptime:%s\n", linebuff,
             tempid, tempseq, temptime);
      continue;
    }

    if (!strcmp(tempid, name)) {
      snprintf(linebuff, sizeof(linebuff), "%s %s\n", tempseq, temptime);
      fputs(linebuff, oufp);
      fflush(oufp);
    }
  }
}

void module_parser_four(FILE *infp, FILE *oufp, const char *recvname,
                        const char *sendname) {
  char linebuff[512] = {0};
  char tempid[64] = {0};
  char tempseq1[64] = {0};
  char tempseq2[64] = {0};
  char temptime[64] = {0};
  char recvid[64] = {0};
  char recvseq1[64] = {0};
  char recvseq2[64] = {0};
  char recvtime[64] = {0};

  while (!feof(infp)) {
    memset(linebuff, 0, sizeof(linebuff));
    fgets(linebuff, sizeof(linebuff), infp);
    if (sscanf(linebuff, "[%[^]]][%[^]]][%[^]]][%[^]]]", tempid, tempseq1,
               tempseq2, temptime) != 4) {
      memset(recvid, 0, sizeof(recvid));
      printf("sscanf %s error, tempid:%s,tempseq1:%s,tempseq2:%s,temptime:%s\n",
             linebuff, tempid, tempseq1, tempseq2, temptime);
      continue;
    }

    if (!strcmp(tempid, recvname)) {
      memcpy(recvid, tempid, sizeof(recvid));
      memcpy(recvseq1, tempseq1, sizeof(recvseq1));
      memcpy(recvseq2, tempseq2, sizeof(recvseq2));
      memcpy(recvtime, temptime, sizeof(recvtime));
      continue;
    }

    if (strlen(recvid) == 0) {
      continue;
    }

    if (!strcmp(tempid, sendname)) {
      snprintf(linebuff, sizeof(linebuff), "%s %s %s %s\n", recvseq1, recvtime,
               tempseq2, temptime);
      fputs(linebuff, oufp);
      fflush(oufp);
      memset(recvid, 0, sizeof(recvid));
    } else {
      memset(recvid, 0, sizeof(recvid));
      printf("buffdata is error, linebuff:%s\n", linebuff);
    }
  }
}

void moudle_monitor_fusion_parser(const char *filepath) {
  FILE *fp = fopen(filepath, "r");
  if (fp == NULL) {
    printf("%s fopen error\n", filepath);
    exit(-1);
  }

  FILE *controllfp = fopen(MONITOR_FUSION_FILENAME, "w+");
  if (controllfp == NULL) {
    printf("%s fopen error\n", MONITOR_FUSION_FILENAME);
    exit(-1);
  }

  module_parser_third(fp, controllfp, TRACE_MONITOR_FUSION);
}

void moudle_monitor_prediction_parser(const char *filepath) {
  FILE *fp = fopen(filepath, "r");
  if (fp == NULL) {
    printf("%s fopen error\n", filepath);
    exit(-1);
  }

  FILE *controllfp = fopen(MONITOR_PREDICTION_FILENAME, "w+");
  if (controllfp == NULL) {
    printf("%s fopen error\n", MONITOR_PREDICTION_FILENAME);
    exit(-1);
  }

  module_parser_third(fp, controllfp, TRACE_MONITOR_PREDICTION);
}

void moudle_canbus_parser(const char *filepathrecv, const char *filepathsend) {
  FILE *fprecv = fopen(filepathrecv, "r");
  if (fprecv == NULL) {
    printf("%s fopen error\n", filepathrecv);
    exit(-1);
  }

  FILE *fpsend = fopen(filepathsend, "r");
  if (fpsend == NULL) {
    printf("%s fopen error\n", filepathsend);
    exit(-1);
  }

  FILE *canbusfp = fopen(CANBUS_FILENAME, "w+");
  if (canbusfp == NULL) {
    printf("%s fopen error\n", CANBUS_FILENAME);
    exit(-1);
  }

  char recvlinebuff[512] = {0};
  char sendlinebuff[512] = {0};
  char sendid[64] = {0};
  unsigned long long sendseq1 = 0;
  unsigned long long sendseq2 = 0;
  unsigned long long sendtime = 0;
  char recvid[64] = {0};
  unsigned long long recvseq1 = 0;
  unsigned long long recvseq2 = 0;
  unsigned long long recvtime = 0;

  while (!feof(fprecv) && !feof(fpsend)) {
    memset(sendlinebuff, 0, sizeof(sendlinebuff));
    fgets(sendlinebuff, sizeof(sendlinebuff), fpsend);
    if (sscanf(sendlinebuff, "[%[^]]][%lld][%lld][%lld]", sendid, &sendseq1,
               &sendseq2, &sendtime) != 4) {
      memset(sendid, 0, sizeof(sendid));
      printf(
          "sscanf %s error, "
          "tempid:%s,tempseq1:%lld,tempseq2:%lld,temptime:%lld\n",
          sendlinebuff, sendid, sendseq1, sendseq2, sendtime);
      continue;
    }

  recvread:
    if (sendseq1 > recvseq2) {
      if (feof(fprecv)) {
        printf("recv file is end\n");
        exit(-1);
      }
      memset(recvlinebuff, 0, sizeof(recvlinebuff));
      fgets(recvlinebuff, sizeof(recvlinebuff), fprecv);
      if (sscanf(recvlinebuff, "[%[^]]][%lld][%lld][%lld]", recvid, &recvseq1,
                 &recvseq2, &recvtime) != 4) {
        memset(recvid, 0, sizeof(recvid));
        recvseq2 = 0;
        printf(
            "sscanf %s error, "
            "tempid:%s,tempseq1:%lld,tempseq2:%lld,temptime:%lld\n",
            recvlinebuff, recvid, recvseq1, recvseq2, recvtime);
      }
      goto recvread;
    } else if (sendseq1 < recvseq2) {
      continue;
    }

    if (!strcmp(recvid, TRACE_CANBUSRECV) &&
        !strcmp(sendid, TRACE_CANBUSSEND)) {
      snprintf(sendlinebuff, sizeof(sendlinebuff), "%lld %lld %lld %lld\n",
               recvseq1, recvtime, sendseq2, sendtime);
      fputs(sendlinebuff, canbusfp);
      fflush(canbusfp);
    }

    sendseq1 = 0;
    recvseq2 = 0;
  }
}

void moudle_controll_parser(const char *filepath) {
  FILE *fp = fopen(filepath, "r");
  if (fp == NULL) {
    printf("%s fopen error\n", filepath);
    exit(-1);
  }

  FILE *controllfp = fopen(CONTROLL_FILENAME, "w+");
  if (controllfp == NULL) {
    printf("%s fopen error\n", CONTROLL_FILENAME);
    exit(-1);
  }

  module_parser_four(fp, controllfp, TRACE_CONTROLLRECV, TRACE_CONTROLLSEND);
}

void moudle_planning_parser(const char *filepath) {
  FILE *fp = fopen(filepath, "r");
  if (fp == NULL) {
    printf("%s fopen error\n", filepath);
    exit(-1);
  }

  FILE *planningfp = fopen(PLANNING_FILENAME, "w+");
  if (planningfp == NULL) {
    printf("%s fopen error\n", PLANNING_FILENAME);
    exit(-1);
  }

  module_parser_four(fp, planningfp, TRACE_PLANNINGRECV, TRACE_PLANNINGSEND);
}

void moudle_prediction_parser(const char *filepath) {
  FILE *fp = fopen(filepath, "r");
  if (fp == NULL) {
    printf("%s fopen error\n", filepath);
    exit(-1);
  }

  FILE *predictionfp = fopen(PREDICTION_FILENAME, "w+");
  if (predictionfp == NULL) {
    printf("%s fopen error\n", PREDICTION_FILENAME);
    exit(-1);
  }

  module_parser_four(fp, predictionfp, TRACE_PREDICTIONRECV,
                     TRACE_PREDICTIONSEND);
}

void moudle_fusion_parser(const char *filepath) {
  FILE *fp = fopen(filepath, "r");
  if (fp == NULL) {
    printf("%s fopen error\n", filepath);
    exit(-1);
  }

  FILE *fusionfp = fopen(FUSION_FILENAME, "w+");
  if (fusionfp == NULL) {
    printf("%s fopen error\n", FUSION_FILENAME);
    exit(-1);
  }

  char linebuff[512] = {0};
  char tempid[64] = {0};
  unsigned long long tempseq1 = 0;
  unsigned long long tempseq2 = 0;
  char temptime[64] = {0};
  char recvid[64] = {0};
  unsigned long long recvseq1 = 0;
  unsigned long long recvseq2 = 0;
  char recvtime[64] = {0};
  while (!feof(fp)) {
    memset(linebuff, 0, sizeof(linebuff));
    fgets(linebuff, sizeof(linebuff), fp);
    if (sscanf(linebuff, "[%[^]]][%lld][%lld][%[^]]]", tempid, &tempseq1,
               &tempseq2, temptime) != 4) {
      memset(recvid, 0, sizeof(recvid));
      printf(
          "sscanf %s error, "
          "tempid:%s,tempseq1:%lld,tempseq2:%lld,temptime:%s\n",
          linebuff, tempid, tempseq1, tempseq2, temptime);
      continue;
    }

    if (!strcmp(tempid, TRACE_FUSION_HDLIDAR)) {
      memcpy(recvid, tempid, sizeof(recvid));
      recvseq1 = tempseq1;
      recvseq2 = tempseq2;
      memcpy(recvtime, temptime, sizeof(recvtime));
      continue;
    }

    if (strlen(recvid) == 0) {
      continue;
    }

    if (!strcmp(tempid, TRACE_FUSIONSEND) && recvseq2 == tempseq1) {
      snprintf(linebuff, sizeof(linebuff), "%lld %s %lld %s\n", recvseq1,
               recvtime, tempseq2, temptime);
      fputs(linebuff, fusionfp);
      fflush(fusionfp);
      memset(recvid, 0, sizeof(recvid));
    }
  }
}

void moudle_lidar_parser(const char *filepathrecv, const char *filepathsend) {
  FILE *fprecv = fopen(filepathrecv, "r");
  if (fprecv == NULL) {
    printf("%s fopen error\n", filepathrecv);
    exit(-1);
  }

  FILE *fpsend = fopen(filepathsend, "r");
  if (fpsend == NULL) {
    printf("%s fopen error\n", filepathsend);
    exit(-1);
  }

  FILE *lidarfp = fopen(HDLIDAR_FILENAME, "w+");
  if (lidarfp == NULL) {
    printf("%s fopen error\n", HDLIDAR_FILENAME);
    exit(-1);
  }

  char recvlinebuff[512] = {0};
  char sendlinebuff[512] = {0};
  char sendid[64] = {0};
  unsigned long long sendseq1 = 0;
  unsigned long long sendseq2 = 0;
  unsigned long long sendlidartime = 0;
  char sendtime[64] = {0};
  char recvid[64] = {0};
  unsigned long long recvseq1 = 0;
  unsigned long long recvseq2 = 0;
  unsigned long long recvlidartime = 0;
  char recvtime[64] = {0};

  while (!feof(fprecv) && !feof(fpsend)) {
    memset(sendlinebuff, 0, sizeof(sendlinebuff));
    fgets(sendlinebuff, sizeof(sendlinebuff), fpsend);
    if (sscanf(sendlinebuff, "[%[^]]][%lld][%lld][%lld][%[^]]]", sendid,
               &sendseq1, &sendseq2, &sendlidartime, sendtime) != 5) {
      memset(sendid, 0, sizeof(sendid));
      printf(
          "sscanf %s error, "
          "tempid:%s,tempseq1:%lld,tempseq2:%lld,sendlidartime:%lld.temptime:%"
          "s\n",
          sendlinebuff, sendid, sendseq1, sendseq2, sendlidartime, sendtime);
      continue;
    }

  recvread:
    if (sendlidartime > recvlidartime) {
      if (feof(fprecv)) {
        printf("recv file is end\n");
        exit(-1);
      }
      memset(recvlinebuff, 0, sizeof(recvlinebuff));
      fgets(recvlinebuff, sizeof(recvlinebuff), fprecv);
      if (sscanf(recvlinebuff, "[%[^]]][%lld][%lld][%lld][%[^]]]", recvid,
                 &recvseq1, &recvseq2, &recvlidartime, recvtime) != 5) {
        memset(recvid, 0, sizeof(recvid));
        recvlidartime = 0;
        printf(
            "sscanf %s error, "
            "tempid:%s,tempseq1:%lld,tempseq2:%lld,sendlidartime:%lld.temptime:"
            "%s\n",
            recvlinebuff, recvid, recvseq1, recvseq2, recvlidartime, recvtime);
      }
      goto recvread;
    } else if (sendlidartime < recvlidartime) {
      continue;
    }

    if (!strcmp(recvid, TRACE_HDLIDARALGORRECV) &&
        !strcmp(sendid, TRACE_HDLIDARALGORSEND)) {
      snprintf(sendlinebuff, sizeof(sendlinebuff), "%lld %lld %s %lld %s\n",
               recvlidartime, recvseq1, recvtime, sendseq2, sendtime);
      fputs(sendlinebuff, lidarfp);
      fflush(lidarfp);
    }

    recvlidartime = 0;
    sendlidartime = 0;
  }
}

int main(int argc, char **argv) {
  if (argc < 3) {
    printf("params is error\n");
    exit(-1);
  }

  if (!strcmp(argv[1], "canbus")) {
    moudle_canbus_parser(argv[2], argv[3]);
  } else if (!strcmp(argv[1], "controll")) {
    moudle_controll_parser(argv[2]);
  } else if (!strcmp(argv[1], "planning")) {
    moudle_planning_parser(argv[2]);
  } else if (!strcmp(argv[1], "prediction")) {
    moudle_prediction_parser(argv[2]);
  } else if (!strcmp(argv[1], "fusion")) {
    moudle_fusion_parser(argv[2]);
  } else if (!strcmp(argv[1], "lidar")) {
    moudle_lidar_parser(argv[2], argv[3]);
  } else if (!strcmp(argv[1], "monitor_fusion")) {
    moudle_monitor_fusion_parser(argv[2]);
  } else if (!strcmp(argv[1], "monitor_prediction")) {
    moudle_monitor_prediction_parser(argv[2]);
  } else {
    printf("%s is not support\n", argv[1]);
  }

  return 0;
}
