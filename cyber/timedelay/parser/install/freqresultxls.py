# # 合并单元格
# ws.merge_cells('A1:E1')
# 设置单元格对齐
# ws['A1'].alignment = Alignment(horizontal='center',vertical="center")
# 设置单元格背景色
# ws['A1'].fill=PatternFill('solid',fgColor='FFFF00')
# 设置单元格边框
# ws['A1'].border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

from openpyxl import Workbook
from openpyxl import load_workbook
from openpyxl.styles import Alignment,PatternFill,GradientFill,Border,Side,numbers
from openpyxl.utils import get_column_letter
from openpyxl.chart import BarChart, Reference, Series
from openpyxl.chart.label import DataLabelList
from openpyxl.drawing.text import CharacterProperties
import re
import statistics as stats

xlsxcolid=['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W',
           'X','Y','Z','AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP',
           'AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ','BA','BB','BC','BD','BE','BF','BG','BH','BI','BJ','BK']
xlsxrawid=['0','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19']

# 加载 Excel 文件
wb = load_workbook('target/AD控制器模块延迟及频率统计.xlsx')
ws = wb.create_sheet("频率结果", 2)

def set_lidar_title(startpos):
  """激光雷达"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="曝光时间"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="激光雷达"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+4]+xlsxrawid[rawid])

def set_fusion_title(startpos):
  """融合"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="融合"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_intradomain_transmission_title(startpos):
  """域内传输"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="域内传输"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+1]+xlsxrawid[rawid])

def set_prediction_title(startpos):
  """预测"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="预测"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_crossdomain_transmission_title(startpos):
  """跨域传输"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="跨域传输"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+1]+xlsxrawid[rawid])

def set_planning_title(startpos):
  """规划"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="规划"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_controll_title(startpos):
  """控制"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="控制"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_canbus_title(startpos):
  """CANBUS"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="CANBUS"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_result_title(startpos):
  """频率耗时结果"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="激光雷达曝光"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="激光雷达接收"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="激光雷达发送"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="融合接收"
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]="融合发送"
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]]="预测接收"
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]]="预测发送"
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]]="规划接收"
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]]="规划发送"
  ws[xlsxcolid[startpos+9]+xlsxrawid[rawid]]="控制接收"
  ws[xlsxcolid[startpos+10]+xlsxrawid[rawid]]="控制发送"
  ws[xlsxcolid[startpos+11]+xlsxrawid[rawid]]="CANBUS接收"
  ws[xlsxcolid[startpos+12]+xlsxrawid[rawid]]="CANBUS发送"
  ws[xlsxcolid[startpos+13]+xlsxrawid[rawid]]="域内传输"
  ws[xlsxcolid[startpos+14]+xlsxrawid[rawid]]="域外传输"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="频率耗时结果"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+14]+xlsxrawid[rawid])

def set_format_title(startpos):
  """统计结果"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="激光雷达曝光"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="激光雷达接收"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="激光雷达发送"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="融合接收"
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]="融合发送"
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]]="预测接收"
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]]="预测发送"
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]]="规划接收"
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]]="规划发送"
  ws[xlsxcolid[startpos+9]+xlsxrawid[rawid]]="控制接收"
  ws[xlsxcolid[startpos+10]+xlsxrawid[rawid]]="控制发送"
  ws[xlsxcolid[startpos+11]+xlsxrawid[rawid]]="CANBUS接收"
  ws[xlsxcolid[startpos+12]+xlsxrawid[rawid]]="CANBUS发送"
  ws[xlsxcolid[startpos+13]+xlsxrawid[rawid]]="域内传输"
  ws[xlsxcolid[startpos+14]+xlsxrawid[rawid]]="域外传输"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="统计结果"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+14]+xlsxrawid[rawid])
  rawid=3
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="平均值"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+14]+xlsxrawid[rawid])
  rawid=5
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="最大值"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+14]+xlsxrawid[rawid])
  rawid=7
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="最小值"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+14]+xlsxrawid[rawid])

def set_title():
  set_lidar_title(0) # A:E 0-4
  set_fusion_title(5) # F:I 5-8
  set_intradomain_transmission_title(9) # J:K 9-10
  set_prediction_title(11) # L:O 11-14
  set_crossdomain_transmission_title(15) #P:Q 15-16
  set_planning_title(17) # R:U 17 - 20
  set_controll_title(21) # V:Y 21-24
  set_canbus_title(25) # Z:AC 25-28
  set_result_title(29) #AD:AR 29-43

def read_data_to_excel(filepath):
  """"从文本文件中逐行读取数据并写入Excel表格"""
  with open(filepath, 'r') as file:
    for line in file:
      data = line.split(' ')
      data[29] = float(data[29])
      data[30] = float(data[30])
      data[31] = float(data[31])
      data[32] = float(data[32])
      data[33] = float(data[33])
      data[34] = float(data[34])
      data[35] = float(data[35])
      data[36] = float(data[36])
      data[37] = float(data[37])
      data[38] = float(data[38])
      data[39] = float(data[39])
      data[40] = float(data[40])
      data[41] = float(data[41])
      data[42] = float(data[42])
      data[43] = float(data[43])
      ws.append(data)

def set_auto_columns():
  dims = {}
  for row in ws.rows:
    for cell in row:
      cell.alignment = Alignment(horizontal='center',vertical="center")
      if cell.value:
        cell_len = 0.7*len(re.findall('([\u4e00-\u9fa5])', str(cell.value))) + len(str(cell.value))
        dims[cell.column] = max((dims.get(cell.column, 0), cell_len))            
  for col, value in dims.items():
    ws.column_dimensions[get_column_letter(col)].width = value + 2

def set_styles():
  set_auto_columns()

def get_format_val():
  startpos = 44
  set_format_title(startpos) #AS:AU 44-59
  column_num = 29
  data_lidar_expouse = []
  data_lidar_recv = []
  data_lidar_send = []
  data_fusion_recv = []
  data_fusion_send = []
  data_prediction_recv = []
  data_prediction_send = []
  data_planning_recv = []
  data_planning_send = []
  data_controll_recv = []
  data_controll_send = []
  data_canbus_recv = []
  data_canbus_send = []
  data_intradomain = []
  data_crossdomain = []
  for row in ws.iter_rows(min_row=3,values_only=True):
    data_lidar_expouse.append(row[column_num + 0])
    data_lidar_recv.append(row[column_num + 1])
    data_lidar_send.append(row[column_num + 2])
    data_fusion_recv.append(row[column_num + 3])
    data_fusion_send.append(row[column_num + 4])
    data_prediction_recv.append(row[column_num + 5])
    data_prediction_send.append(row[column_num + 6])
    data_planning_recv.append(row[column_num + 7])
    data_planning_send.append(row[column_num + 8])
    data_controll_recv.append(row[column_num + 9])
    data_controll_send.append(row[column_num + 10])
    data_canbus_recv.append(row[column_num + 11])
    data_canbus_send.append(row[column_num + 12])
    data_intradomain.append(row[column_num + 13])
    data_crossdomain.append(row[column_num + 14])
  rawid=4
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_lidar_expouse))
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_lidar_recv))
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_lidar_send))
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_fusion_recv))
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_fusion_send))
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_prediction_recv))
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_prediction_send))
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_planning_recv))
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_planning_send))
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_controll_recv))
  ws[xlsxcolid[startpos+9]+xlsxrawid[rawid]]  = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_controll_send))
  ws[xlsxcolid[startpos+10]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_canbus_recv))
  ws[xlsxcolid[startpos+11]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_canbus_send))
  ws[xlsxcolid[startpos+12]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_intradomain))
  ws[xlsxcolid[startpos+13]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(nonzero_values)))
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_crossdomain))
  ws[xlsxcolid[startpos+14]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(nonzero_values)))
  rawid=6
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]  = max(data_lidar_expouse)
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]  = max(data_lidar_recv)
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]  = max(data_lidar_send)
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]  = max(data_fusion_recv)
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]  = max(data_fusion_send)
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]]  = max(data_prediction_recv)
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]]  = max(data_prediction_send)
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]]  = max(data_planning_recv)
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]]  = max(data_planning_send)
  ws[xlsxcolid[startpos+9]+xlsxrawid[rawid]]  = max(data_controll_recv)
  ws[xlsxcolid[startpos+10]+xlsxrawid[rawid]] = max(data_controll_send)
  ws[xlsxcolid[startpos+11]+xlsxrawid[rawid]] = max(data_canbus_recv)
  ws[xlsxcolid[startpos+12]+xlsxrawid[rawid]] = max(data_canbus_send)
  ws[xlsxcolid[startpos+13]+xlsxrawid[rawid]] = max(data_intradomain)
  ws[xlsxcolid[startpos+14]+xlsxrawid[rawid]] = max(data_crossdomain)
  rawid=8
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_lidar_expouse))
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_lidar_recv))
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_lidar_send))
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_fusion_recv))
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_fusion_send))
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_prediction_recv))
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_prediction_send))
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_planning_recv))
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_planning_send))
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_controll_recv))
  ws[xlsxcolid[startpos+9]+xlsxrawid[rawid]]  = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_controll_send))
  ws[xlsxcolid[startpos+10]+xlsxrawid[rawid]] = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_canbus_recv))
  ws[xlsxcolid[startpos+11]+xlsxrawid[rawid]] = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_canbus_send))
  ws[xlsxcolid[startpos+12]+xlsxrawid[rawid]] = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_intradomain))
  ws[xlsxcolid[startpos+13]+xlsxrawid[rawid]] = min(nonzero_values)
  nonzero_values = list(filter(lambda x: x != None and x != 0, data_crossdomain))
  ws[xlsxcolid[startpos+14]+xlsxrawid[rawid]] = min(nonzero_values)

def set_result_barchart(rawid, position, title):
  chart = BarChart()
  data = Reference(ws, min_col=rawid, min_row=3, max_col=rawid, max_row=ws.max_row)
  chart.add_data(data, titles_from_data=True)
  chart.title = title
  chart.style = 5
  chart.y_axis.title="消耗时间 单位毫秒(ms)"
  chart.x_axis.delete = True
  chart.legend=None
  chart.width=30
  chart.height=6
  ws.add_chart(chart, position)

def set_format_barchart(colid, rawid, position, title):
  chart = BarChart()
  data = Reference(ws, min_col=colid, max_col=colid+14,
  min_row=rawid, max_row=rawid)
  cats = Reference(ws, min_col=colid, max_col=colid+14,
  min_row=2, max_row=2)
  series = Series(data)
  chart.append(series)
  chart.set_categories(cats)
  chart.type = "col"
  chart.title = title
  chart.style = 5
  chart.y_axis.title="消耗时间 单位毫秒(ms)"
  chart.width=30
  chart.height=6
  chart.dataLabels = DataLabelList()
  chart.dataLabels.showVal=True
  chart.legend=None
  ws.add_chart(chart, position)

def set_barchart():
  set_result_barchart(30, "AS10", "激光雷达曝光频率")
  set_result_barchart(31, "AS25", "激光雷达接收频率")
  set_result_barchart(32, "AS40", "激光雷达发送频率")
  set_result_barchart(33, "AS55", "融合接收频率")
  set_result_barchart(34, "AS70", "融合发送频率")
  set_result_barchart(35, "AS85", "预测接收频率")
  set_result_barchart(36, "AS100", "预测发送频率")
  set_result_barchart(37, "AS115", "规划接收频率")
  set_result_barchart(38, "AS130", "规划发送频率")
  set_result_barchart(39, "AS145", "控制接收频率")
  set_result_barchart(40, "AS160", "控制发送频率")
  set_result_barchart(41, "AS175", "CANBUS接收频率")
  set_result_barchart(42, "AS190", "CANBUS发送频率")
  set_result_barchart(43, "AS205", "域内传输频率")
  set_result_barchart(44, "AS220", "跨域传输频率")
  set_format_barchart(45, 4, "AS235", "平均频率")
  set_format_barchart(45, 6, "AS250", "最大频率")
  set_format_barchart(45, 8, "AS265", "最小频率")

def complete_excel():
  # 保存工作簿为Excel文件
  set_styles()
  wb.save('target/AD控制器模块延迟及频率统计.xlsx')
  wb.close()

def __main__():
  set_title()
  read_data_to_excel('target/freqresult.log')
  get_format_val()
  set_barchart()
  complete_excel()

__main__()
