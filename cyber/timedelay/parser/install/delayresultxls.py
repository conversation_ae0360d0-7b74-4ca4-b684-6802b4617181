# # 合并单元格
# ws.merge_cells('A1:E1')
# 设置单元格对齐
# ws['A1'].alignment = Alignment(horizontal='center',vertical="center")
# 设置单元格背景色
# ws['A1'].fill=PatternFill('solid',fgColor='FFFF00')
# 设置单元格边框
# ws['A1'].border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

from openpyxl import Workbook
from openpyxl.styles import Alignment,PatternFill,GradientFill,Border,Side,numbers
from openpyxl.utils import get_column_letter
from openpyxl.chart import BarChart, Reference, Series
from openpyxl.chart.label import DataLabelList
from openpyxl.drawing.text import CharacterProperties
import re
import statistics as stats

xlsxcolid=['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W',
           'X','Y','Z','AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP',
           'AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ']
xlsxrawid=['0','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19']

# 打开或创建一个新的工作簿
wb = Workbook()
ws=wb.active
ws.title="延迟结果"

def set_lidar_title(startpos):
  """激光雷达"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="曝光时间"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="激光雷达"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+4]+xlsxrawid[rawid])

def set_fusion_title(startpos):
  """融合"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="融合"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_intradomain_transmission_title(startpos):
  """域内传输"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="域内传输"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+1]+xlsxrawid[rawid])

def set_prediction_title(startpos):
  """预测"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="预测"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_crossdomain_transmission_title(startpos):
  """跨域传输"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="跨域传输"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+1]+xlsxrawid[rawid])

def set_planning_title(startpos):
  """规划"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="规划"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_controll_title(startpos):
  """控制"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="控制"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_canbus_title(startpos):
  """CANBUS"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="接收时间"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="序号"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="发送时间"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="CANBUS"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+3]+xlsxrawid[rawid])

def set_result_title(startpos):
  """延迟耗时结果"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="激光雷达"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="融合"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="预测"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="规划"
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]="控制"
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]]="CANBUS"
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]]="域内传输"
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]]="域外传输"
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]]="总耗时"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="延迟耗时结果"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+8]+xlsxrawid[rawid])

def set_format_title(startpos):
  """统计结果"""
  rawid=2
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="激光雷达"
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]]="融合"
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]]="预测"
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]]="规划"
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]]="控制"
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]]="CANBUS"
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]]="域内传输"
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]]="域外传输"
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]]="总耗时"
  rawid=1
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="统计结果"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+8]+xlsxrawid[rawid])
  rawid=3
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="平均值"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+8]+xlsxrawid[rawid])
  rawid=5
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="最大值"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+8]+xlsxrawid[rawid])
  rawid=7
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]]="最小值"
  ws.merge_cells(xlsxcolid[startpos+0]+xlsxrawid[rawid]+":"+xlsxcolid[startpos+8]+xlsxrawid[rawid])

def set_title():
  set_lidar_title(0) # A:E 0-4
  set_fusion_title(5) # F:I 5-8
  set_intradomain_transmission_title(9) # J:K 9-10
  set_prediction_title(11) # L:O 11-14
  set_crossdomain_transmission_title(15) #P:Q 15-16
  set_planning_title(17) # R:U 17 - 20
  set_controll_title(21) # V:Y 21-24
  set_canbus_title(25) # Z:AC 25-28
  set_result_title(29) #AD:AL 29-37

def read_data_to_excel(filepath):
  """"从文本文件中逐行读取数据并写入Excel表格"""
  with open(filepath, 'r') as file:
    for line in file:
      data = line.split(' ')
      data[29] = float(data[29])
      data[30] = float(data[30])
      data[31] = float(data[31])
      data[32] = float(data[32])
      data[33] = float(data[33])
      data[34] = float(data[34])
      data[35] = float(data[35])
      data[36] = float(data[36])
      data[37] = float(data[37])
      ws.append(data)

def set_auto_columns():
  dims = {}
  for row in ws.rows:
    for cell in row:
      cell.alignment = Alignment(horizontal='center',vertical="center")
      if cell.value:
        cell_len = 0.7*len(re.findall('([\u4e00-\u9fa5])', str(cell.value))) + len(str(cell.value))
        dims[cell.column] = max((dims.get(cell.column, 0), cell_len))            
  for col, value in dims.items():
    ws.column_dimensions[get_column_letter(col)].width = value + 2

def set_styles():
  set_auto_columns()

def get_format_val():
  startpos = 38
  set_format_title(startpos) #AM:AU 38-46
  column_num = 29
  data_lidar = []
  data_fusion = []
  data_prediction = []
  data_planning = []
  data_controll = []
  data_canbus = []
  data_intradomain = []
  data_crossdomain = []
  data_result = []
  for row in ws.iter_rows(min_row=3,values_only=True):
    data_lidar.append(row[column_num + 0])
    data_fusion.append(row[column_num + 1])
    data_prediction.append(row[column_num + 2])
    data_planning.append(row[column_num + 3])
    data_controll.append(row[column_num + 4])
    data_canbus.append(row[column_num + 5])
    data_intradomain.append(row[column_num + 6])
    data_crossdomain.append(row[column_num + 7])
    data_result.append(row[column_num + 8])
  rawid=4
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_lidar)))
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_fusion)))
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_prediction)))
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_planning)))
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_controll)))
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_canbus)))
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_intradomain)))
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_crossdomain)))
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]] = float("{:.3f}".format(stats.mean(data_result)))
  rawid=6
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]] = max(data_lidar)
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]] = max(data_fusion)
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]] = max(data_prediction)
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]] = max(data_planning)
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]] = max(data_controll)
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]] = max(data_canbus)
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]] = max(data_intradomain)
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]] = max(data_crossdomain)
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]] = max(data_result)
  rawid=8
  ws[xlsxcolid[startpos+0]+xlsxrawid[rawid]] = min(data_lidar)
  ws[xlsxcolid[startpos+1]+xlsxrawid[rawid]] = min(data_fusion)
  ws[xlsxcolid[startpos+2]+xlsxrawid[rawid]] = min(data_prediction)
  ws[xlsxcolid[startpos+3]+xlsxrawid[rawid]] = min(data_planning)
  ws[xlsxcolid[startpos+4]+xlsxrawid[rawid]] = min(data_controll)
  ws[xlsxcolid[startpos+5]+xlsxrawid[rawid]] = min(data_canbus)
  ws[xlsxcolid[startpos+6]+xlsxrawid[rawid]] = min(data_intradomain)
  ws[xlsxcolid[startpos+7]+xlsxrawid[rawid]] = min(data_crossdomain)
  ws[xlsxcolid[startpos+8]+xlsxrawid[rawid]] = min(data_result)

def set_result_barchart(rawid, position, title):
  chart = BarChart()
  data = Reference(ws, min_col=rawid, min_row=3, max_col=rawid, max_row=ws.max_row)
  chart.add_data(data, titles_from_data=True)
  chart.title = title
  chart.style = 5
  chart.y_axis.title="消耗时间 单位毫秒(ms)"
  chart.x_axis.delete = True
  chart.legend=None
  chart.width=30
  chart.height=6
  ws.add_chart(chart, position)

def set_format_barchart(colid, rawid, position, title):
  chart = BarChart()
  data = Reference(ws, min_col=colid, max_col=colid+8,
  min_row=rawid, max_row=rawid)
  cats = Reference(ws, min_col=colid, max_col=colid+8,
  min_row=2, max_row=2)
  series = Series(data)
  chart.append(series)
  chart.set_categories(cats)
  chart.type = "col"
  chart.title = title
  chart.style = 5
  chart.y_axis.title="消耗时间 单位毫秒(ms)"
  chart.width=30
  chart.height=6
  chart.dataLabels = DataLabelList()
  chart.dataLabels.showVal=True
  chart.legend=None
  ws.add_chart(chart, position)

def set_barchart():
  set_result_barchart(30, "AM10", "激光雷达耗时")
  set_result_barchart(31, "AM25", "融合耗时")
  set_result_barchart(32, "AM40", "预测耗时")
  set_result_barchart(33, "AM55", "规划耗时")
  set_result_barchart(34, "AM70", "控制耗时")
  set_result_barchart(35, "AM85", "CANBUS耗时")
  set_result_barchart(36, "AM100", "域内传输耗时")
  set_result_barchart(37, "AM115", "跨域传输耗时")
  set_result_barchart(38, "AM130", "总链路耗时")
  set_format_barchart(39, 4, "AM145", "平均耗时")
  set_format_barchart(39, 6, "AM160", "最大耗时")
  set_format_barchart(39, 8, "AM175", "最小耗时")

def complete_excel():
  # 保存工作簿为Excel文件
  set_styles()
  wb.save('target/AD控制器模块延迟及频率统计.xlsx')

def __main__():
  set_title()
  read_data_to_excel('target/delayresult.log')
  get_format_val()
  set_barchart()
  complete_excel()

__main__()
