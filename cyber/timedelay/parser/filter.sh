#!/bin/bash
TARGETDIR=$(pwd)/target
moduleparser=install/moduleparser
delayparser=install/delayparser
frequenceparser=install/frequenceparser

hdlidarrecv_trace="lidar_algor_recv"
hdlidarsend_trace="lidar_algor_send"
canbusrecv_trace="canbus_recv"
canbussend_trace="canbus_send"

defaultid=Default
canbusid=Canbus
controllid=Controll
planningid=Planning
predictionid=Prediction
fusionid=Fusion
lidarid=Lidar

canbusfile=""
controllfile=""
planningfile=""
predictionfile=""
fusionfile=""
lidarfile=""
monitor_fusionfile=""
monitor_predictionfile=""

function usage()
{
    echo "usage:"
    echo "  ./filter.sh log \"2024-02-20 15:52:23\" \"2024-02-20 15:53:23\""
    echo "  最后两个参数可选，分别为开始时间、结束时间，如果只设置一个时间，则必须是开始时间"
    exit
}

function filter_module_orgin()
{
    cat $1 | grep -e "$2" -e "$3" > $TARGETDIR/$4.temp
}

function module_is_exist()
{
  local file=$1
  local module=$2

  if echo "$file" | grep -q "$module"; then
    if [ -f $file ];then
      return 0
    else
      echo "$file is not exist"
      return 1
    fi
  else
    return 1
  fi
}

function file_is_exist()
{
  dir=$1
  module=$2

  for file in $(find $dir -type f); do
    if [[ $file == *$module* ]];then
      echo "$file"
      return 0
    fi
  done
  return -1
}

function file_module_chkset()
{
  dir=$1
  canbusfile=$(file_is_exist $dir delay_Canbus)
  if [[ $canbusfile == "" ]]; then
    echo "canbus is not exist"
    exit
  fi
  controllfile=$(file_is_exist $dir delay_Controll)
  if [[ $controllfile == "" ]]; then
    echo "controll is not exist"
    exit
  fi
  planningfile=$(file_is_exist $dir delay_Planning)
  if [[ $planningfile == "" ]]; then
    echo "planning is not exist"
    exit
  fi
  predictionfile=$(file_is_exist $dir delay_Prediction)
  if [[ $predictionfile == "" ]]; then
    echo "prediction is not exist"
    exit
  fi
  fusionfile=$(file_is_exist $dir delay_Fusion)
  if [[ $fusionfile == "" ]]; then
    echo "fusion is not exist"
    exit
  fi
  lidarfile=$(file_is_exist $dir delay_Lidar)
  if [[ $lidarfile == "" ]]; then
    echo "lidar is not exist"
    exit
  fi
  monitor_fusionfile=$(file_is_exist $dir delay_Monitor_adsens)
  if [[ $monitor_fusionfile == "" ]]; then
    echo "monitor_fusion is not exist"
    exit
  fi
  monitor_predictionfile=$(file_is_exist $dir delay_Monitor_adplan)
  if [[ $monitor_predictionfile == "" ]]; then
    echo "monitor_prediction is not exist"
    exit
  fi
}

function file_tag_separate()
{
  rm -rf $TARGETDIR
  mkdir $TARGETDIR
  cat $lidarfile | grep -a $hdlidarrecv_trace > $TARGETDIR/$hdlidarrecv_trace.log
  cat $lidarfile | grep -a $hdlidarsend_trace > $TARGETDIR/$hdlidarsend_trace.log

  cat $canbusfile | grep -a $canbusrecv_trace > $TARGETDIR/$canbusrecv_trace.log
  cat $canbusfile | grep -a $canbussend_trace > $TARGETDIR/$canbussend_trace.log
}

function module_parser()
{
  ./$moduleparser canbus $TARGETDIR/$canbusrecv_trace.log $TARGETDIR/$canbussend_trace.log
  ./$moduleparser controll $controllfile
  ./$moduleparser planning $planningfile
  ./$moduleparser prediction $predictionfile
  ./$moduleparser fusion $fusionfile
  ./$moduleparser lidar $TARGETDIR/$hdlidarrecv_trace.log $TARGETDIR/$hdlidarsend_trace.log
  ./$moduleparser monitor_fusion $monitor_fusionfile
  ./$moduleparser monitor_prediction $monitor_predictionfile
}

function delay_parser()
{
  echo "# run ./$delayparser $TARGETDIR "$1" "$2""
  # if [[ $1 != "" ]] && [[ $2 != "" ]];then
  #   echo "------------"
  #   ./$delayparser $TARGETDIR "$1" "$2"
  # elif [[ $1 != "" ]];then
  #   ./$delayparser $TARGETDIR "$1"
  # else
  #   ./$delayparser $TARGETDIR
  # fi
  ./$delayparser $TARGETDIR "$1" "$2"
  echo "# run ./$frequenceparser $TARGETDIR delayresult.log"
  ./$frequenceparser $TARGETDIR delayresult.log
}

function start()
{
  if [[ $# < 1 ]];then
    usage
  fi
  file_module_chkset $1

  file_tag_separate

  module_parser

  delay_parser "$2" "$3"

  echo "# run python3 install/delayresultxls.py"
  python3 install/delayresultxls.py
  echo "# run python3 install/freqresultxls.py"
  python3 install/freqresultxls.py
  echo "complete!!!"
}

start "$@"
