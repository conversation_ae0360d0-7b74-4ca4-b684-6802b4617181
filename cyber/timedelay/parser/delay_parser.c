#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <time.h>
#include <unistd.h>

#define CANBUS_FILENAME "canbus.parser.log"
#define CONTROLL_FILENAME "controll.parser.log"
#define PLANNING_FILENAME "planning.parser.log"
#define PREDICTION_FILENAME "prediction.parser.log"
#define FUSION_FILENAME "fusion.parser.log"
#define HDLIDAR_FILENAME "lidar.parser.log"
#define MONITOR_PREDICTION_FILENAME "monitor_prediction.parser.log"
#define MONITOR_FUSION_FILENAME "monitor_fusion.parser.log"

#define DELAYRESULT_FILENAME "delayresult.log"

static char canbusbuff[512];
static char controllbuff[512];
static char planningbuff[512];
static char predictionbuff[512];
static char fusionbuff[512];
static char lidarbuff[512];
static char monitor_predictionbuff[512];
static char monitor_fusionbuff[512];

int canbus_to_controll_parser(FILE *backfp, FILE *frontfp) {
  char frontlinebuff[512] = {0};
  char backlinebuff[512] = {0};
  unsigned long long frontseq = 0;
  unsigned long long fronttime = 0;
  unsigned long long backseq = 0;
  unsigned long long backtime = 0;

  memset(canbusbuff, 0, sizeof(canbusbuff));
  memset(controllbuff, 0, sizeof(controllbuff));
  while (!feof(backfp) && !feof(frontfp)) {
    memset(backlinebuff, 0, sizeof(backlinebuff));
    fgets(backlinebuff, sizeof(backlinebuff), backfp);
    if (sscanf(backlinebuff, "%lld %lld", &backseq, &backtime) != 2) {
      backseq = 0;
      backtime = 0;
      printf("sscanf %s error, backseq:%lld,backtime:%lld,\n", backlinebuff,
             backseq, backtime);
      continue;
    }

  recvread:
    if (backseq > frontseq) {
      if (feof(frontfp)) {
        printf("frontfp file is end\n");
        exit(-1);
      }
      memset(frontlinebuff, 0, sizeof(frontlinebuff));
      fgets(frontlinebuff, sizeof(frontlinebuff), frontfp);
      if (sscanf(frontlinebuff, "%*[^ ] %*[^ ] %lld %lld", &frontseq,
                 &fronttime) != 2) {
        frontseq = 0;
        fronttime = 0;
        printf("sscanf %s error, frontseq:%lld,fronttime:%lld\n", frontlinebuff,
               frontseq, fronttime);
      }
      goto recvread;
    } else if (backseq < frontseq) {
      continue;
    }

    char *p = NULL;
    p = strstr(backlinebuff, "\n");
    if (p) {
      *p = '\0';
    }
    p = strstr(frontlinebuff, "\n");
    if (p) {
      *p = '\0';
    }
    memcpy(canbusbuff, backlinebuff, sizeof(canbusbuff));
    memcpy(controllbuff, frontlinebuff, sizeof(controllbuff));
    return 0;
  }

  if (feof(backfp) || feof(frontfp)) {
    printf("file is end\n");
    exit(-1);
  }
  return -1;
}

int controll_to_planning_parser(FILE *frontfp, char *backlinebuff) {
  unsigned long long frontseq = 0;
  unsigned long long fronttime = 0;
  unsigned long long backseq = 0;
  unsigned long long backtime = 0;

  if (sscanf(backlinebuff, "%lld %lld", &backseq, &backtime) != 2) {
    printf("sscanf %s error, backseq:%lld,backtime:%lld,\n", backlinebuff,
           backseq, backtime);
    return -1;
  }

  while (!feof(frontfp)) {
    if (strlen(planningbuff) <= 0) {
      fgets(planningbuff, sizeof(planningbuff), frontfp);
    }
    if (sscanf(planningbuff, "%*[^ ] %*[^ ] %lld %lld", &frontseq,
               &fronttime) != 2) {
      frontseq = 0;
      fronttime = 0;
      printf("sscanf %s error, frontseq:%lld,fronttime:%lld,\n", planningbuff,
             frontseq, fronttime);
      continue;
    }

    if (backseq > frontseq) {
      memset(planningbuff, 0, sizeof(planningbuff));
      continue;
    } else if (backseq < frontseq) {
      return -1;
    }

    char *p = strstr(planningbuff, "\n");
    if (p) {
      *p = '\0';
    }
    return 0;
  }

  if (feof(frontfp)) {
    printf("file is end\n");
    exit(-1);
  }

  return -1;
}

int planning_to_prediction_parser(FILE *frontfp, char *backlinebuff) {
  unsigned long long frontseq = 0;
  unsigned long long fronttime = 0;
  unsigned long long backseq = 0;
  unsigned long long backtime = 0;

  if (sscanf(backlinebuff, "%lld %lld", &backseq, &backtime) != 2) {
    printf("sscanf %s error, backseq:%lld,backtime:%lld,\n", backlinebuff,
           backseq, backtime);
    return -1;
  }

  while (!feof(frontfp)) {
    if (strlen(predictionbuff) <= 0) {
      fgets(predictionbuff, sizeof(predictionbuff), frontfp);
    }
    if (sscanf(predictionbuff, "%*[^ ] %*[^ ] %lld %lld", &frontseq,
               &fronttime) != 2) {
      frontseq = 0;
      fronttime = 0;
      printf("sscanf %s error, frontseq:%lld,fronttime:%lld,\n", predictionbuff,
             frontseq, fronttime);
      continue;
    }

    if (backseq > frontseq) {
      memset(predictionbuff, 0, sizeof(predictionbuff));
      continue;
    } else if (backseq < frontseq) {
      return -1;
    }

    char *p = strstr(predictionbuff, "\n");
    if (p) {
      *p = '\0';
    }
    return 0;
  }

  if (feof(frontfp)) {
    printf("file is end\n");
    exit(-1);
  }

  return -1;
}

int prediction_to_fusion_parser(FILE *frontfp, char *backlinebuff) {
  unsigned long long frontseq = 0;
  unsigned long long fronttime = 0;
  unsigned long long backseq = 0;
  unsigned long long backtime = 0;

  if (sscanf(backlinebuff, "%lld %lld", &backseq, &backtime) != 2) {
    printf("sscanf %s error, backseq:%lld,backtime:%lld,\n", backlinebuff,
           backseq, backtime);
    return -1;
  }

  while (!feof(frontfp)) {
    if (strlen(fusionbuff) <= 0) {
      fgets(fusionbuff, sizeof(fusionbuff), frontfp);
    }
    if (sscanf(fusionbuff, "%*[^ ] %*[^ ] %lld %lld", &frontseq, &fronttime) !=
        2) {
      frontseq = 0;
      fronttime = 0;
      printf("sscanf %s error, frontseq:%lld,fronttime:%lld,\n", fusionbuff,
             frontseq, fronttime);
      continue;
    }

    if (backseq > frontseq) {
      memset(fusionbuff, 0, sizeof(fusionbuff));
      continue;
    } else if (backseq < frontseq) {
      return -1;
    }

    char *p = strstr(fusionbuff, "\n");
    if (p) {
      *p = '\0';
    }
    return 0;
  }

  if (feof(frontfp)) {
    printf("file is end\n");
    exit(-1);
  }

  return -1;
}

int fusion_to_lidar_parser(FILE *frontfp, char *backlinebuff) {
  unsigned long long frontseq = 0;
  unsigned long long fronttime = 0;
  unsigned long long backseq = 0;
  unsigned long long backtime = 0;

  if (sscanf(backlinebuff, "%lld %lld", &backseq, &backtime) != 2) {
    printf("sscanf %s error, backseq:%lld,backtime:%lld,\n", backlinebuff,
           backseq, backtime);
    return -1;
  }

  while (!feof(frontfp)) {
    if (strlen(lidarbuff) <= 0) {
      fgets(lidarbuff, sizeof(lidarbuff), frontfp);
    }
    if (sscanf(lidarbuff, "%*[^ ] %*[^ ] %*[^ ] %lld %lld", &frontseq,
               &fronttime) != 2) {
      frontseq = 0;
      fronttime = 0;
      printf("sscanf %s error, frontseq:%lld,fronttime:%lld,\n", lidarbuff,
             frontseq, fronttime);
      continue;
    }

    if (backseq > frontseq) {
      memset(lidarbuff, 0, sizeof(lidarbuff));
      continue;
    } else if (backseq < frontseq) {
      return -1;
    }

    char *p = strstr(lidarbuff, "\n");
    if (p) {
      *p = '\0';
    }
    return 0;
  }

  if (feof(frontfp)) {
    printf("file is end\n");
    exit(-1);
  }

  return -1;
}

int monitor_prediction_parser(FILE *frontfp, char *backlinebuff) {
  unsigned long long frontseq = 0;
  unsigned long long fronttime = 0;
  unsigned long long backseq = 0;
  unsigned long long backtime = 0;

  if (sscanf(backlinebuff, "%lld %lld", &backseq, &backtime) != 2) {
    printf("sscanf %s error, backseq:%lld,backtime:%lld,\n", backlinebuff,
           backseq, backtime);
    return -1;
  }

  while (!feof(frontfp)) {
    if (strlen(monitor_predictionbuff) <= 0) {
      fgets(monitor_predictionbuff, sizeof(monitor_predictionbuff), frontfp);
    }
    if (sscanf(monitor_predictionbuff, "%lld %lld", &frontseq, &fronttime) !=
        2) {
      frontseq = 0;
      fronttime = 0;
      printf("sscanf %s error, frontseq:%lld,fronttime:%lld,\n",
             monitor_predictionbuff, frontseq, fronttime);
      continue;
    }

    if (backseq > frontseq) {
      memset(monitor_predictionbuff, 0, sizeof(monitor_predictionbuff));
      continue;
    } else if (backseq < frontseq) {
      return -1;
    }

    char *p = strstr(monitor_predictionbuff, "\n");
    if (p) {
      *p = '\0';
    }
    return 0;
  }

  if (feof(frontfp)) {
    printf("file is end\n");
    exit(-1);
  }

  return -1;
}

int monitor_fusion_parser(FILE *frontfp, char *backlinebuff) {
  unsigned long long frontseq = 0;
  unsigned long long fronttime = 0;
  unsigned long long backseq = 0;
  unsigned long long backtime = 0;

  if (sscanf(backlinebuff, "%lld %lld", &backseq, &backtime) != 2) {
    printf("sscanf %s error, backseq:%lld,backtime:%lld,\n", backlinebuff,
           backseq, backtime);
    return -1;
  }

  while (!feof(frontfp)) {
    if (strlen(monitor_fusionbuff) <= 0) {
      fgets(monitor_fusionbuff, sizeof(monitor_fusionbuff), frontfp);
    }
    if (sscanf(monitor_fusionbuff, "%lld %lld", &frontseq, &fronttime) != 2) {
      frontseq = 0;
      fronttime = 0;
      printf("sscanf %s error, frontseq:%lld,fronttime:%lld,\n",
             monitor_fusionbuff, frontseq, fronttime);
      continue;
    }

    if (backseq > frontseq) {
      memset(monitor_fusionbuff, 0, sizeof(monitor_fusionbuff));
      continue;
    } else if (backseq < frontseq) {
      return -1;
    }

    char *p = strstr(monitor_fusionbuff, "\n");
    if (p) {
      *p = '\0';
    }
    return 0;
  }

  if (feof(frontfp)) {
    printf("file is end\n");
    exit(-1);
  }

  return -1;
}

time_t datetime2sec(int year, int mon, int day, int hour, int min, int sec) {
  struct tm tt;
  memset(&tt, 0, sizeof(tt));
  tt.tm_year = year - 1900;
  tt.tm_mon = mon - 1;
  tt.tm_mday = day;
  tt.tm_hour = hour;
  tt.tm_min = min;
  tt.tm_sec = sec;
  return mktime(&tt);
}

// ./app.out logpath "2024-2-2 12:14:16" "2024-2-2 13:14:16"
int main(int argc, char **argv) {
  if (argc < 2 || access(argv[1], F_OK)) {
    printf("input params error!\n");
    return -1;
  }

  long long starttimestamp = 0;
  long long endtimestamp = 0;
  int year, mon, day, hour, min, sec;
  if (argv[2] && strlen(argv[2]) > 0) {
    if (sscanf(argv[2], "%d-%d-%d %d:%d:%d", &year, &mon, &day, &hour, &min,
               &sec) != 6) {
      printf("start time error! %s\n", argv[2]);
      exit(-1);
    }
    starttimestamp = datetime2sec(year, mon, day, hour, min, sec);
    starttimestamp = starttimestamp * 1000000000;
    printf("start time:[%s][%lld]\n", argv[2], starttimestamp);
  }

  if (argv[3] && strlen(argv[3]) > 0) {
    if (sscanf(argv[3], "%d-%d-%d %d:%d:%d", &year, &mon, &day, &hour, &min,
               &sec) != 6) {
      printf("end time error! %s \n", argv[2]);
      exit(-1);
    }
    endtimestamp = datetime2sec(year, mon, day, hour, min, sec);
    endtimestamp = endtimestamp * 1000000000;
    printf("end time:[%s][%lld]\n", argv[3], endtimestamp);
  }

  char filepath[2048];
  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1], CANBUS_FILENAME);
  FILE *canbusfp = fopen(filepath, "r");
  if (canbusfp == NULL) {
    printf("canbusfp fopen error!\n");
    return -1;
  }
  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1], CONTROLL_FILENAME);
  FILE *controllfp = fopen(filepath, "r");
  if (controllfp == NULL) {
    printf("controllfp fopen error!\n");
    return -1;
  }
  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1], PLANNING_FILENAME);
  FILE *planningfp = fopen(filepath, "r");
  if (planningfp == NULL) {
    printf("planningfp fopen error!\n");
    return -1;
  }
  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1], PREDICTION_FILENAME);
  FILE *predictionfp = fopen(filepath, "r");
  if (predictionfp == NULL) {
    printf("predictionfp fopen error!\n");
    return -1;
  }
  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1], FUSION_FILENAME);
  FILE *fusionfp = fopen(filepath, "r");
  if (fusionfp == NULL) {
    printf("fusionfp fopen error!\n");
    return -1;
  }
  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1], HDLIDAR_FILENAME);
  FILE *hdlidarfp = fopen(filepath, "r");
  if (hdlidarfp == NULL) {
    printf("hdlidarfp fopen error!\n");
    return -1;
  }
  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1],
           MONITOR_PREDICTION_FILENAME);
  FILE *monitor_predictionfp = fopen(filepath, "r");
  if (monitor_predictionfp == NULL) {
    printf("monitor_predictionfp fopen error!\n");
    return -1;
  }
  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1],
           MONITOR_FUSION_FILENAME);
  FILE *monitor_fusionfp = fopen(filepath, "r");
  if (monitor_fusionfp == NULL) {
    printf("monitor_fusionfp fopen error!\n");
    return -1;
  }

  snprintf(filepath, sizeof(filepath), "%s/%s", argv[1], DELAYRESULT_FILENAME);
  FILE *delayresultfp = fopen(filepath, "w+");
  if (delayresultfp == NULL) {
    printf("delayresultfp fopen error!\n");
    return -1;
  }

  while (1) {
    if (canbus_to_controll_parser(canbusfp, controllfp) != 0) {
      continue;
    }

    if (controll_to_planning_parser(planningfp, controllbuff) != 0) {
      continue;
    }

    if (planning_to_prediction_parser(predictionfp, planningbuff) != 0) {
      continue;
    }

    if (monitor_prediction_parser(monitor_predictionfp, planningbuff) != 0) {
      continue;
    }

    if (prediction_to_fusion_parser(fusionfp, predictionbuff) != 0) {
      continue;
    }

    if (monitor_fusion_parser(monitor_fusionfp, predictionbuff) != 0) {
      continue;
    }

    if (fusion_to_lidar_parser(hdlidarfp, fusionbuff) != 0) {
      continue;
    }

    long long lidarexposure, lidarsend, fusonsend, intradomaintrans,
        predictionsend, crossdomaintrans, planningsend, controllsend,
        canbussend;
    long long tmpval1, tmpval2, tmpval3, tmpval4, tmpval5;
    if (sscanf(lidarbuff, "%lld %lld %lld %lld %lld", &lidarexposure, &tmpval1,
               &tmpval2, &tmpval3, &lidarsend) != 5) {
      printf("lidarbuff %s is error\n", lidarbuff);
      exit(-1);
    }
    if (sscanf(fusionbuff, "%lld %lld %lld %lld", &tmpval1, &tmpval2, &tmpval3,
               &fusonsend) != 4) {
      printf("fusionbuff %s is error\n", fusionbuff);
      exit(-1);
    }
    if (sscanf(monitor_fusionbuff, "%lld %lld", &tmpval1, &intradomaintrans) !=
        2) {
      printf("monitor_fusionbuff %s is error\n", monitor_fusionbuff);
      exit(-1);
    }
    if (sscanf(predictionbuff, "%lld %lld %lld %lld", &tmpval1, &tmpval2,
               &tmpval3, &predictionsend) != 4) {
      printf("predictionbuff %s is error\n", predictionbuff);
      exit(-1);
    }
    if (sscanf(monitor_predictionbuff, "%lld %lld", &tmpval1,
               &crossdomaintrans) != 2) {
      printf("monitor_predictionbuff %s is error\n", monitor_predictionbuff);
      exit(-1);
    }
    if (sscanf(planningbuff, "%lld %lld %lld %lld", &tmpval1, &tmpval2,
               &tmpval3, &planningsend) != 4) {
      printf("planningbuff %s is error\n", planningbuff);
      exit(-1);
    }
    if (sscanf(controllbuff, "%lld %lld %lld %lld", &tmpval1, &tmpval2,
               &tmpval3, &controllsend) != 4) {
      printf("controllbuff %s is error\n", controllbuff);
      exit(-1);
    }
    if (sscanf(canbusbuff, "%lld %lld %lld %lld", &tmpval1, &tmpval2, &tmpval3,
               &canbussend) != 4) {
      printf("canbusbuff %s is error\n", canbusbuff);
      exit(-1);
    }

    if ((starttimestamp > 0) && (lidarsend <= starttimestamp)) {
      continue;
    }

    if ((endtimestamp > 0) && (lidarsend >= endtimestamp)) {
      continue;
    }

    int ret = snprintf(
        filepath, sizeof(filepath),
        "%s %s %s %s %s %s %s %s %0.3f %0.3f %0.3f %0.3f %0.3f %0.3f %0.3f "
        "%0.3f %0.3f\n",
        lidarbuff, fusionbuff, monitor_fusionbuff, predictionbuff,
        monitor_predictionbuff, planningbuff, controllbuff, canbusbuff,
        (lidarsend - lidarexposure) / 1000000.0,
        (fusonsend - lidarsend) / 1000000.0,
        (predictionsend - fusonsend) / 1000000.0,
        (planningsend - predictionsend) / 1000000.0,
        (controllsend - planningsend) / 1000000.0,
        (canbussend - controllsend) / 1000000.0,
        (intradomaintrans - fusonsend) / 1000000.0,
        (crossdomaintrans - predictionsend) / 1000000.0,
        (canbussend - lidarexposure) / 1000000.0);
    fputs(filepath, delayresultfp);
    fflush(delayresultfp);
  }
}
