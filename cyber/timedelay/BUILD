load("//tools:cpplint.bzl", "cpplint")
load("//tools:apollo_package.bzl", "apollo_cc_library", "apollo_package", "apollo_cc_test")
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_import")
load("//tools/platform:build_defs.bzl", "if_aarch64", "if_x86_64")

package(default_visibility = ["//visibility:public"])

cc_import(
    name = "lwrb_shared",
    shared_library = select({
        "@platforms//cpu:x86_64": "lwrb/x86_64_lib/lib/liblwrb.so",
        "@platforms//cpu:aarch64": "lwrb/aarch64_lib/lib/liblwrb.so",
    }),
)

apollo_cc_library(
    name = "timedelay",
    hdrs = ["timedelay.h"],
    deps = [
        ":timewrite"
    ],
)

apollo_cc_library(
    name = "timewrite",
    hdrs = [
        "timewrite.h",
        "lwrb/include/lwrb.h",
    ],
    linkopts = [
        "-fvisibility=hidden",
        "-Wl,-rpath,$$ORIGIN",
        "-lstdc++fs",
        "-llwrb",
    ] + if_x86_64([        
        "-Wl,-rpath,$$ORIGIN/../../src/cyber/timedelay/lwrb/x86_64_lib/lib",
        "-Wl,-rpath,$$ORIGIN/../../../src/cyber/timedelay/lwrb/x86_64_lib/lib",
        "-Wl,-rpath,$$ORIGIN/../../../../src/cyber/timedelay/lwrb/x86_64_lib/lib",
        "-Wl,-rpath,/opt/apollo/neo/src/cyber/timedelay/lwrb/x86_64_lib/lib",
    ]) + if_aarch64([
        "-Wl,-rpath,$$ORIGIN/../../src/cyber/timedelay/lwrb/aarch64_lib/lib",
        "-Wl,-rpath,$$ORIGIN/../../../src/cyber/timedelay/lwrb/aarch64_lib/lib",
        "-Wl,-rpath,$$ORIGIN/../../../../src/cyber/timedelay/lwrb/aarch64_lib/lib",
        "-Wl,-rpath,/opt/apollo/neo/src/cyber/timedelay/lwrb/aarch64_lib/lib",
    ]),
    deps = [
        ":lwrb_shared",
        "//third_party/concurrent_queue",
    ],
)

apollo_package()
cpplint()
