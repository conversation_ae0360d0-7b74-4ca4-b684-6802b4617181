/******************************************************************************
 * Copyright 2018 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#ifndef CYBER_IO_POLL_DATA_H_
#define CYBER_IO_POLL_DATA_H_

#include <sys/epoll.h>

#include <cstdint>
#include <functional>

namespace apollo {
namespace cyber {
namespace io {

struct PollResponse {
  explicit PollResponse(uint32_t e = 0) : events(e) {}

  uint32_t events;
};

struct PollRequest {
  int fd = -1;
  uint32_t events = 0;
  int timeout_ms = -1;
  std::function<void(const PollResponse&)> callback = nullptr;
};

struct PollCtrlParam {
  int operation;
  int fd;
  epoll_event event;
};

}  // namespace io
}  // namespace cyber
}  // namespace apollo

#endif  // CYBER_IO_POLL_DATA_H_
