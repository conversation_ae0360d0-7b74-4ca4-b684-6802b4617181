/******************************************************************************
 * Copyright 2023 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "gtest/gtest.h"

#include "cyber/profiler/profiler.h"

TEST(ProfilerTest, single_block) {
  PERF_BLOCK("block")
  for (int i = 0; i < 1000; ++i) {
  }
  PERF_BLOCK_END
}

TEST(ProfilerTest, multi_block) {
  PERF_BLOCK("block1")
  for (int i = 0; i < 500; ++i) {
  }
  PERF_BLOCK_END

  for (int i = 0; i < 500; ++i) {
  }

  PERF_BLOCK("block2")
  for (int i = 0; i < 500; ++i) {
  }
  PERF_BLOCK_END
}

TEST(ProfilerTest, nested_block) {
  PERF_BLOCK("outer_block")
  for (int i = 0; i < 500; ++i) {
  }

  PERF_BLOCK("inner_block")
  for (int i = 0; i < 500; ++i) {
  }
  PERF_BLOCK_END

  for (int i = 0; i < 500; ++i) {
  }
  PERF_BLOCK_END
}

TEST(ProfilerTest, function) {
  PERF_FUNCTION()
  for (int i = 0; i < 1000; ++i) {
  }
}
