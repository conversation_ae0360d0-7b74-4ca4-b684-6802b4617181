syntax = "proto2";

package apollo.cyber.proto;

message ClassicTask {
  optional string name = 1;
  optional uint32 prio = 2 [default = 1];
  optional string group_name = 3;
}

message SchedGroup {
  required string name = 1 [default = "default_grp"];
  optional uint32 processor_num = 2;
  optional string affinity = 3;
  optional string cpuset = 4;
  optional string processor_policy = 5;
  optional int32 processor_prio = 6 [default = 0];
  repeated ClassicTask tasks = 7;
}

message ClassicConf {
  repeated SchedGroup groups = 1;
}
