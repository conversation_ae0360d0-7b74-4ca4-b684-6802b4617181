syntax = "proto2";

package apollo.cyber.proto;

enum OptionalMode {
  HYBRID = 0;
  INTRA = 1;
  SHM = 2;
  RTPS = 3;
}

message ShmMulticastLocator {
  optional string ip = 1;
  optional uint32 port = 2;
};

message ArenaChannelConf {
  optional string channel_name = 1;
  // the acutal arena segment size is equal with max_msg_size * max_pool_size + meta,
  // so max_msg_size * max_pool_size should be less than the limit of ArenaAddressAllocator:
  // 2^31 - 128 * 1024 * 1024, which is hardcode in the underlying implementation
  optional uint64 max_msg_size = 2 [default = 33554432];
  optional uint64 max_pool_size = 3 [default = 32];
  optional uint64 shared_buffer_size = 4 [default = 0];
};

message ArenaShmConf {
  repeated ArenaChannelConf arena_channel_conf = 1;
};

message ShmConf {
  optional string notifier_type = 1;
  optional string shm_type = 2;
  optional ShmMulticastLocator shm_locator = 3;
  optional ArenaShmConf arena_shm_conf = 4;
};

message RtpsParticipantAttr {
  optional int32 lease_duration = 1 [default = 12];
  optional int32 announcement_period = 2 [default = 3];
  optional uint32 domain_id_gain = 3 [default = 200];
  optional uint32 port_base = 4 [default = 10000];
};

message CommunicationMode {
  optional OptionalMode same_proc = 1 [default = INTRA];  // INTRA SHM RTPS
  optional OptionalMode diff_proc = 2 [default = SHM];    // SHM RTPS
  optional OptionalMode diff_host = 3 [default = RTPS];   // RTPS
};

message ResourceLimit {
  optional uint32 max_history_depth = 1 [default = 1000];
};

message TransportConf {
  optional ShmConf shm_conf = 1;
  optional RtpsParticipantAttr participant_attr = 2;
  optional CommunicationMode communication_mode = 3;
  optional ResourceLimit resource_limit = 4;
};
