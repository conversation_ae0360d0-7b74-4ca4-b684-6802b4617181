syntax = "proto2";

package apollo.cyber.proto;

message UnitTest {
  optional string class_name = 1;
  optional string case_name = 2;
};

message Chatter {
  optional uint64 timestamp = 1;
  optional uint64 lidar_timestamp = 2;
  optional uint64 seq = 3;
  optional bytes content = 4;
};

message ChatterBenchmark {
  optional uint64 stamp = 1;
  optional uint64 seq = 2;
  optional string content = 3;
}

