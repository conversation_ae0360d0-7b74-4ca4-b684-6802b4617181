syntax = "proto2";

package apollo.cyber.proto;

message ChoreographyTask {
  optional string name = 1;
  optional int32 processor = 2;
  optional uint32 prio = 3 [default = 1];
}

message ChoreographyConf {
  optional uint32 choreography_processor_num = 1;
  optional string choreography_affinity = 2;
  optional string choreography_processor_policy = 3;
  optional int32 choreography_processor_prio = 4;
  optional string choreography_cpuset = 5;
  optional uint32 pool_processor_num = 6;
  optional string pool_affinity = 7;
  optional string pool_processor_policy = 8;
  optional int32 pool_processor_prio = 9;
  optional string pool_cpuset = 10;
  repeated ChoreographyTask tasks = 11;
}
