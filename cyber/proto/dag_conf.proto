syntax = "proto2";

package apollo.cyber.proto;

import "cyber/proto/component_conf.proto";

message ComponentInfo {
  optional string class_name = 1;
  optional ComponentConfig config = 2;
}

message TimerComponentInfo {
  optional string class_name = 1;
  optional TimerComponentConfig config = 2;
}

message ModuleConfig {
  optional string module_library = 1;
  repeated ComponentInfo components = 2;
  repeated TimerComponentInfo timer_components = 3;
}

message DagConfig {
  repeated ModuleConfig module_config = 1;
}
