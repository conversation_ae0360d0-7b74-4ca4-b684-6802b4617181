## Auto generated by `proto_build_generator.py`
load("//tools/proto:proto.bzl", "proto_library")
load("//tools:apollo_package.bzl", "apollo_package")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "topology_change_proto",
    srcs = ["topology_change.proto"],
    deps = [
        ":role_attributes_proto",
    ],
)

proto_library(
    name = "dag_conf_proto",
    srcs = ["dag_conf.proto"],
    deps = [
        ":component_conf_proto",
    ],
)

proto_library(
    name = "proto_desc_proto",
    srcs = ["proto_desc.proto"],
)

proto_library(
    name = "choreography_conf_proto",
    srcs = ["choreography_conf.proto"],
)

proto_library(
    name = "record_proto",
    srcs = ["record.proto"],
)

proto_library(
    name = "component_conf_proto",
    srcs = ["component_conf.proto"],
    deps = [
        ":qos_profile_proto",
    ],
)

proto_library(
    name = "cyber_conf_proto",
    srcs = ["cyber_conf.proto"],
    deps = [
        ":perf_conf_proto",
        ":run_mode_conf_proto",
        ":scheduler_conf_proto",
        ":transport_conf_proto",
    ],
)

proto_library(
    name = "perf_conf_proto",
    srcs = ["perf_conf.proto"],
)

proto_library(
    name = "classic_conf_proto",
    srcs = ["classic_conf.proto"],
)

proto_library(
    name = "parameter_proto",
    srcs = ["parameter.proto"],
)

proto_library(
    name = "unit_test_proto",
    srcs = ["unit_test.proto"],
)

proto_library(
    name = "simple_proto",
    srcs = ["simple.proto"],
)

proto_library(
    name = "scheduler_conf_proto",
    srcs = ["scheduler_conf.proto"],
    deps = [
        ":choreography_conf_proto",
        ":classic_conf_proto",
    ],
)

proto_library(
    name = "transport_conf_proto",
    srcs = ["transport_conf.proto"],
)

proto_library(
    name = "qos_profile_proto",
    srcs = ["qos_profile.proto"],
)

proto_library(
    name = "run_mode_conf_proto",
    srcs = ["run_mode_conf.proto"],
)

proto_library(
    name = "role_attributes_proto",
    srcs = ["role_attributes.proto"],
    deps = [
        ":qos_profile_proto",
    ],
)

proto_library(
    name = "clock_proto",
    srcs = ["clock.proto"],
)

proto_library(
    name = "threadlib_conf_proto",
    srcs = ["threadlib_conf.proto"],
)

proto_library(
    name = "tcp_transport_proto",
    srcs = ["tcp_transport.proto"],
)

apollo_package()
