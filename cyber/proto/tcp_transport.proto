syntax = "proto2";

package apollo.cyber.proto;

message TcpChannel {
  optional bool enable = 1 [default = false];
  optional string channel_name = 2;
  optional string owner_ip = 3;
  optional string url_port = 4;
};

message TcpTransport {
  optional bool globle_enable = 1 [default = false];
  optional string default_port = 2;
  repeated string domain_ips = 3;

  repeated TcpChannel tcp_channels = 4;
}
