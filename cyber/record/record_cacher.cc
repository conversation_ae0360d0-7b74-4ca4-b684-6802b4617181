/******************************************************************************
 * Copyright 2023 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "cyber/record/record_cacher.h"

#include <dirent.h>
#include <math.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/statfs.h>
#include <sys/types.h>

#include <iomanip>
#include <iostream>
#include <memory>
#include <utility>
#include <vector>

#include "cyber/common/log.h"
#include "cyber/common/time_conversion.h"
#include "cyber/record/record_gflags.h"

namespace apollo {
namespace cyber {
namespace record {
namespace {
const uint16_t kMaxStringLenth = 256;
const uint32_t kCacheSeconds = 35;
const uint64_t kSecond2Nanosecond = 1000 * 1000 * 1000;
}  // namespace

using apollo::cyber::common::UnixSecondsToString;
using proto::Channel;
using proto::SingleMessage;

std::string RecordCacher::data_path_ = "";  // NOLINT

RecordCacher::RecordCacher(const std::string& record_path)
    : record_path_(record_path) {
  header_ = HeaderBuilder::GetHeader();
  {
    std::lock_guard<std::mutex> lock(single_msg_cache_mutex_);
    single_msg_cache_.clear();
  }
  write_file_thread_ =
      std::thread( &RecordCacher::ThreadFunc, this);
  stop_flag.store(false);
}

RecordCacher::~RecordCacher() { Close(); }
void RecordCacher::ThreadFunc() {
  while (!stop_flag.load()) {
    auto cur_time = floor(cyber::Time::Now().ToSecond());
    if (cur_time < begin_time_ || cur_time > expire_time_) {
      if (start_write_flag_.exchange(false)) {
        AINFO << "start_write_flag_ :" << start_write_flag_
              << ", is_opened_:" << is_opened_;
        if (is_opened_) {
          file_writer_->Close();
          is_opened_ = false;
        }
      }
    } else {
      if (!start_write_flag_.exchange(true)) {
        Open("mpi_record");
        std::lock_guard<std::mutex> lock(single_msg_cache_mutex_);
        for (const auto& i : channel_message_number_map_) {
          Channel channel;
          channel.set_name(i.first);
          channel.set_message_type(channel_message_type_map_[i.first]);
          channel.set_proto_desc(channel_proto_desc_map_[i.first]);
          if (!file_writer_->WriteChannel(channel)) {
            AERROR << "Failed to write channel for record file: " << path_;
            // return false;
          }
        }
      }

      {
        std::lock_guard<std::mutex> lock(single_msg_cache_mutex_);
        AINFO << "single_msg_cache_ size:" << single_msg_cache_.size();
        auto iter = single_msg_cache_.begin();
        while (iter != single_msg_cache_.end()) {
          SingleMessage single_message;
          for (auto& single_message : *iter) {
            auto msg_time = single_message->time() / kSecond2Nanosecond;
            if (msg_time >= begin_time_.load() && msg_time < cur_time) {
              WriteMessage(*single_message);
            }
          }
          iter = single_msg_cache_.erase(iter);
        }
      }
    }
    cyber::Duration(1.0).Sleep();
  }  // end of while
}

bool RecordCacher::Open(const std::string& file) {
  file_ = file;
  file_index_ = 0;
  sstream_.str(std::string());
  sstream_.clear();
  sstream_ << "." << std::setw(5) << std::setfill('0') << file_index_++;
  if (header_.segment_interval() > 0 || header_.segment_raw_size() > 0) {
    // file_ = UnixSecondsToString(time(nullptr), "%Y%m%d%H%M%S") + ".record";
    // using time of earliest message
    auto file_time = block_time_ - single_msg_cache_.size();
    file_ = UnixSecondsToString(file_time, "%Y%m%d%H%M%S") + ".record";

    path_ = record_path_ + "/" + file_ + sstream_.str();
  } else {
    path_ = record_path_ + "/" + file_;
  }

  segment_raw_size_ = 0;
  segment_begin_time_ = 0;
  file_writer_.reset(new RecordFileWriter());
  if (!file_writer_->Open(path_)) {
    AERROR << "Failed to open output record file: " << path_;
    return false;
  }
  if (!file_writer_->WriteHeader(header_)) {
    AERROR << "Failed to write header: " << path_;
    file_writer_->Close();
    return false;
  }
  is_opened_ = true;
  return is_opened_;
}

void RecordCacher::Close() {
  if (is_opened_) {
    file_writer_->Close();
    is_opened_ = false;
  }

  stop_flag.store(true);
  if (write_file_thread_.joinable()) {
    write_file_thread_.join();
  }
}
bool RecordCacher::SplitOutfile() {
  file_writer_.reset(new RecordFileWriter());
  if (!true) {
    if (file_index_ > 99999) {
      AWARN << "More than 99999 record files had been recored, will restart "
            << "counting from 0.";
      file_index_ = 0;
    }
    sstream_.str(std::string());
    sstream_.clear();
    sstream_ << "." << std::setw(5) << std::setfill('0') << file_index_++;
    path_ = record_path_ + "/" + file_ + sstream_.str();
  } else {
    path_ = record_path_ + "/" +
            UnixSecondsToString(time(nullptr), "%Y%m%d%H%M%S") + ".record";
  }

  segment_raw_size_ = 0;
  segment_begin_time_ = 0;
  if (!file_writer_->Open(path_)) {
    AERROR << "Failed to open record file: " << path_;
    return false;
  }
  if (!file_writer_->WriteHeader(header_)) {
    AERROR << "Failed to write header for record file: " << path_;
    return false;
  }
  for (const auto& i : channel_message_number_map_) {
    Channel channel;
    channel.set_name(i.first);
    channel.set_message_type(channel_message_type_map_[i.first]);
    channel.set_proto_desc(channel_proto_desc_map_[i.first]);
    if (!file_writer_->WriteChannel(channel)) {
      AERROR << "Failed to write channel for record file: " << path_;
      return false;
    }
  }
  return true;
}

bool RecordCacher::WriteChannelCache(const std::string& channel_name,
                                     const std::string& message_type,
                                     const std::string& proto_desc) {
  std::lock_guard<std::mutex> lg(mutex_);
  if (IsNewChannel(channel_name)) {
    OnNewChannel(channel_name, message_type, proto_desc);
    Channel channel;
    channel.set_name(channel_name);
    channel.set_message_type(message_type);
    channel.set_proto_desc(proto_desc);
    if (start_write_flag_.load()) {
      AINFO << "Found new channel when writing file:" << channel_name;
      if (!file_writer_->WriteChannel(channel)) {
        AERROR << "Failed to write channel: " << channel_name;
        return false;
      }
    }

  } else {
    AWARN << "Intercept write channel request, duplicate channel: "
          << channel_name;
  }
  return true;
}

bool RecordCacher::WriteMessage(const SingleMessage& message) {
  std::lock_guard<std::mutex> lg(mutex_);
  OnNewMessage(message.channel_name());
  if (!file_writer_->WriteMessage(message)) {
    AERROR << "Write message is failed.";
    return false;
  }

  segment_raw_size_ += message.content().size();
  if (0 == segment_begin_time_) {
    segment_begin_time_ = message.time();
  }
  if (segment_begin_time_ > message.time()) {
    segment_begin_time_ = message.time();
  }

  if ((header_.segment_interval() > 0 &&
       message.time() - segment_begin_time_ > header_.segment_interval()) ||
      (header_.segment_raw_size() > 0 &&
       segment_raw_size_ > header_.segment_raw_size())) {
    file_writer_backup_.swap(file_writer_);
    file_writer_backup_->Close();
    if (!SplitOutfile()) {
      AERROR << "Split out file is failed.";
      return false;
    }
  }
  return true;
}

bool RecordCacher::WriteMessageCache(
    const std::shared_ptr<SingleMessage>& message) {
  OnNewMessage(message->channel_name());
  auto msg_time = static_cast<uint32_t>(message->time() / kSecond2Nanosecond);

  // insert cache
  if (block_time_ < msg_time) {
    std::lock_guard<std::mutex> lock(single_msg_cache_mutex_);
    if (block_time_ < msg_time) {
      while (single_msg_cache_.size() >= FLAGS_cache_seconds) {
        single_msg_cache_.pop_front();
      }
      single_msg_cache_.emplace_back(std::move(single_msg_block_));
      std::lock_guard<std::mutex> lock(single_msg_block_mutex_);
      single_msg_block_.clear();
      AINFO << "first msg"
            << ", block_time_: " << block_time_
            << ",single_msg_cache_ size:" << single_msg_cache_.size();
      block_time_ = msg_time;
    }
  }
  // insert block : current second data
  {
    std::lock_guard<std::mutex> lock(single_msg_block_mutex_);
    single_msg_block_.emplace_back(message);
    block_time_ = msg_time;
    ADEBUG << "emplace_back "
           << ", block_time_: " << block_time_
           << ",block_size: " << single_msg_block_.size();
  }
  return true;
}

bool RecordCacher::SetSizeOfFileSegmentation(uint64_t size_kilobytes) {
  if (is_opened_) {
    AWARN << "Please call this interface before opening file.";
    return false;
  }
  header_.set_segment_raw_size(size_kilobytes * 1024UL);
  return true;
}

bool RecordCacher::SetIntervalOfFileSegmentation(uint64_t time_sec) {
  if (is_opened_) {
    AWARN << "Please call this interface before opening file.";
    return false;
  }
  header_.set_segment_interval(time_sec * 1000000000UL);
  return true;
}

bool RecordCacher::IsNewChannel(const std::string& channel_name) const {
  return channel_message_number_map_.find(channel_name) ==
         channel_message_number_map_.end();
}

void RecordCacher::OnNewChannel(const std::string& channel_name,
                                const std::string& message_type,
                                const std::string& proto_desc) {
  channel_message_number_map_[channel_name] = 0;
  channel_message_type_map_[channel_name] = message_type;
  channel_proto_desc_map_[channel_name] = proto_desc;
}

void RecordCacher::OnNewMessage(const std::string& channel_name) {
  auto iter = channel_message_number_map_.find(channel_name);
  if (iter != channel_message_number_map_.end()) {
    iter->second++;
  }
}

uint64_t RecordCacher::GetMessageNumber(const std::string& channel_name) const {
  auto search = channel_message_number_map_.find(channel_name);
  if (search != channel_message_number_map_.end()) {
    return search->second;
  }
  return 0;
}

const std::string& RecordCacher::GetMessageType(
    const std::string& channel_name) const {
  auto search = channel_message_type_map_.find(channel_name);
  if (search != channel_message_type_map_.end()) {
    return search->second;
  }
  return kEmptyString;
}

const std::string& RecordCacher::GetProtoDesc(
    const std::string& channel_name) const {
  auto search = channel_proto_desc_map_.find(channel_name);
  if (search != channel_proto_desc_map_.end()) {
    return search->second;
  }
  return kEmptyString;
}

std::set<std::string> RecordCacher::GetChannelList() const {
  std::set<std::string> channel_list;
  for (const auto& item : channel_message_number_map_) {
    channel_list.emplace(item.first);
  }
  return std::move(channel_list);
}

void RecordCacher::UpdateWriteFileTime() {
  if (!stop_flag.load()) {
    auto cur_time = floor(cyber::Time::Now().ToSecond());
    if (0 == begin_time_.load()) {
      begin_time_ = cur_time - FLAGS_cache_seconds;
    }
    expire_time_ = cur_time + FLAGS_cache_seconds;
  }
}

}  // namespace record
}  // namespace cyber
}  // namespace apollo
