/******************************************************************************
 * Copyright 2023 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "cyber/record/record_gflags.h"

DEFINE_bool(use_timestamp_as_filename, true,
            "use the current time stamp as the record-file name");

DEFINE_double(delete_pro, 0.3, "disk space is less than 30%, delete the file");
DEFINE_uint32(cache_seconds, 35, "The number of seconds to cache data");
