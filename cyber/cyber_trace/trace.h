/******************************************************************************
 * Copyright 2023 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#pragma once

#include "cyber/trace/trace.h"

namespace apollo {
namespace trace {

struct SchedTraceConfig {
  static constexpr const char* kFilePrefix = "/apollo/data/log/sched_trace_";
  static constexpr uint8_t kMaxFileNumber = 5;
  static constexpr uint64_t kMaxLogSize = 50UL << 20;
  static constexpr bool kEnableWrite = false;
  static constexpr uint64_t kBatchSize = 128;
  static constexpr uint64_t kHeaderSize = 512UL << 10;
};

struct ShmTraceConfig {
  static constexpr const char* kFilePrefix = "/apollo/data/log/shm_trace_";
  static constexpr uint8_t kMaxFileNumber = 5;
  static constexpr uint64_t kMaxLogSize = 50UL << 20;
  static constexpr bool kEnableWrite = false;
  static constexpr uint64_t kBatchSize = 128;
  static constexpr uint64_t kHeaderSize = 512UL << 10;
};

struct TcpTraceConfig {
  static constexpr const char* kFilePrefix = "/apollo/data/log/tcp_trace_";
  static constexpr uint8_t kMaxFileNumber = 5;
  static constexpr uint64_t kMaxLogSize = 50UL << 20;
  static constexpr bool kEnableWrite = false;
  static constexpr uint64_t kBatchSize = 128;
  static constexpr uint64_t kHeaderSize = 512UL << 10;
};

}  // namespace trace
}  // namespace apollo
