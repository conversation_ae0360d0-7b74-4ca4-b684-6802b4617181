<package format="2">
  <name>cyber</name>
  <version>local</version>
  <description>
    Apollo Cyber RT is an open source, high performance runtime framework designed specifically for autonomous driving scenarios.
    Based on a centralized computing model, it is greatly optimized for high concurrency, low latency, and high throughput in autonomous driving.
  </description>

  <maintainer email="<EMAIL>">Apollo</maintainer>
  <license>Apache License 2.0</license>
  <url type="website">https://www.apollo.auto/</url>
  <url type="repository">https://github.com/ApolloAuto/apollo</url>
  <url type="bugtracker">https://github.com/ApolloAuto/apollo/issues</url>

  <type>module</type>
  <src_path url="https://github.com/ApolloAuto/apollo">//cyber</src_path>

  <depend so_names="ncurses" repo_name="ncurses5">libncurses5-dev</depend>
  <depend so_names="uuid" repo_name="uuid">uuid-dev</depend>

  <depend expose="False">3rd-rules-python</depend>
  <depend expose="False">3rd-grpc</depend>
  <depend expose="False">3rd-rules-proto</depend>
  <depend expose="False">3rd-py</depend>
  <depend expose="False">3rd-gpus</depend>
  <depend expose="False">3rd-bazel-skylib</depend>

  <depend repo_name="com_google_protobuf" lib_names="protobuf">3rd-protobuf</depend>
  <!-- <depend repo_name="fastdds">3rd-fastdds</depend> -->
  <depend repo_name="fastdds" lib_names="fastdds">3rd-fastdds-wrap</depend>
  <depend repo_name="proj">3rd-proj</depend>
  <depend repo_name="eigen">3rd-eigen3</depend>
  <depend repo_name="com_github_google_glog" lib_names="glog">3rd-glog</depend>
  <depend repo_name="com_github_gflags_gflags" lib_names="gflags">3rd-gflags</depend>
  <depend type="binary" repo_name="common-msgs" lib_names="common-msgs">common-msgs</depend>
  <depend expose="False">3rd-cpplint</depend>
  <depend repo_name="com_google_googletest" lib_names="gtest,gtest_main">3rd-gtest</depend>
  <depend repo_name="com_github_nlohmann_json" lib_names="single_json,json">3rd-nlohmann-json</depend>
  <depend repo_name="tinyxml2" lib_names="tinyxml2">libtinyxml2-dev</depend>
  <depend>bazel-extend-tools</depend>

  <depend>libunwind-dev</depend>
  <depend>nethogs</depend>
  <depend>sysstat</depend>
  <depend>gperftools</depend>
  <depend>bvar</depend>

</package>
