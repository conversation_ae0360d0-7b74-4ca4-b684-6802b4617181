# Define all coms in DAG streaming.
module_config {
    module_library : "modules/localization/rs_localization/libreinit_rs_localization_component.so"

    components {
      class_name : "RsLocalizationReinitComponent"
      config {
        name : "rslocalization_reinit"
        readers: [
          {
            channel: "/apollo/mcloud/location_point_init_tcp"
            qos_profile: {
              depth : 10
            }
            pending_queue_size: 50
          }
        ]
      }
    }
}