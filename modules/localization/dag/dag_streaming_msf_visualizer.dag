# Define all coms in DAG streaming.
module_config {
    module_library : "modules/localization/msf/local_tool/local_visualization/online_visual/online_visualizer_compenont.so"

    components {
      class_name : "OnlineVisualizerComponent"
      config {
        name : "msf_visualizer"
        flag_file_path : "/apollo_workspace/modules/localization/conf/localization.conf"
        readers: [
          {
            channel: "/apollo/sensor/rshelios/PointCloud2"
          }
        ]
      }
    }
}
