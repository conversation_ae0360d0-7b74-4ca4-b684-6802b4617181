#--flagfile=/apollo_workspace/modules/common/data/global_flagfile.txt
#--noenable_gps_imu_interprolate

--use_inner_message=false
# Redefine the map_dir of global_flagfile.txt
--map_dir=/apollo_workspace/modules/map

## ndt 
--orign_utm_x=277524.64908120985
--orign_utm_y=3466349.585268318
--orign_utm_z=14.0
--use_pcl_ndt=true
# The directory name of localization map.
# type: string
# default: local_map
--local_map_name=local_map

# Enable gnss timestamp compensator for invalid timestamp
# type: bool
# default: false
--enable_gps_imu_compensate=false

# Enable lidar-based localization.
# type: bool
# defalult: true
--enable_lidar_localization=true

# Localization mode, 0 for intensity, 1 for altitude, 2 for fusion.
# type: int
# default: 2
--lidar_localization_mode=2

# image yaw align mode, 0 for align off, 1 for fusion, 2 for fusion with multithread.
# type: int
# default: 2
--lidar_yaw_align_mode=2

# Lidar filter size
# type: int
# default: 11
--lidar_filter_size=17

# point cloud step
# type: int
# default: 2
--point_cloud_step=2

# The height from the center of lidar to ground plane
# type: double
# default: 1.80
--lidar_height_default=1.80

# idar msg and imu msg max delay time.
# type: double
# default: 0.4
--lidar_imu_lidar_max_delay_time=0.4

# The valid coverage of pointcloud and map.
# type: double
# default: 0.8
--lidar_map_coverage_theshold=0.8

# Lidar debug switch.
# type: bool
# default: false
--lidar_debug_log_flag=false

# integ_ins_can_self_align.
# type: bool
# default: false
--integ_ins_can_self_align=false

# integ_sins_align_with_vel.
# type: bool
# default: true
--integ_sins_align_with_vel=true

# Whether check state of sins. If true, sins will restart in bad state.
# type: bool
# default: true
--integ_sins_state_check=true

# The param of sins state check.
# type: double
# default: 30.0
--integ_sins_state_span_time=30.0

# The param of sins state check.
# type: double
# default: 1.0
--integ_sins_state_pos_std=1.0

# vel_threshold_get_yaw.
# type: double
# default: 5.0
--vel_threshold_get_yaw=5.0

# enable flag ins_aid_rtk.
# type: bool
# default: false
--enable_ins_aid_rtk=false

# eph_buffer_path.
# type: string
# default: ""
--eph_buffer_path=""

# gnss_debug_log_flag.
# type: bool
# default: false
--gnss_debug_log_flag=false

# imu_rate.
# type: double
# default: 1.0
--imu_rate=1.0

# Whether load utm zone id from local map folder
# type: bool
# default: true
--if_utm_zone_id_from_folder=true

# local_utm_zone_id.
# type: int
# default: int
--local_utm_zone_id=10

# trans_gpstime_to_utctime.
# type: bool
# default: true
--trans_gpstime_to_utctime=false

# GNSS Mode, 0 for bestgnss pose, 1 for self gnss.
# type: string
# default: 0
--gnss_mode=0

# The pointcloud topic name.
# type: string
# default: /apollo/sensor/velodyne64/compensator/PointCloud2
--lidar_topic=/apollo/sensor/rshelios/PointCloud2

# The lidar extrinsics file
# type: string
# default: modules/localization/msf/params/velodyne_params/velodyne128_novatel_extrinsics.yaml
--lidar_extrinsics_file=/apollo_workspace/modules/localization/msf/params/rslidar_params/rslidar_huace_extrinsics_example.yaml
--lidar_height_file=/apollo_workspace/modules/localization/msf/params/rslidar_params/rslidar_height.yaml
# imu coordinate, true->flu, false->rfu
# type: bool
# default: true
--imu_coord_rfu=true

# Whether use bestgnsspose as measure after initializaiton.
# type: bool
# default: false
--gnss_only_init=false

# Whether use imu ant leverarm from ant imu yaml file
# type: bool
# default: true
--if_imuant_from_file=true

# Imu ant offset x
# type: double
# default: 0.0
--imu_to_ant_offset_x=0.0

# Imu ant offset y
# type: double
# default: 0.0
--imu_to_ant_offset_y=0.788

# Imu ant offset z
# type: double
# default: 0.0
--imu_to_ant_offset_z=1.077

# Imu ant offset x uncertainty
# type: double
# default: 0.0
--imu_to_ant_offset_ux=0.05

# Imu ant offset y uncertainty
# type: double
# default: 0.0
--imu_to_ant_offset_uy=0.05

# Imu ant offset z uncertainty
# type: double
# default: 0.0
--imu_to_ant_offset_uz=0.08

# Whether load vehicle coord to imu coord quaternion from yaml file
# type: bool
# default: true
--if_vehicle_imu_from_file=true

# vehicle coord to imu coord quaternion qx
# type: double
# default: 0.0
--imu_vehicle_qx=0.0

# vehicle coord to imu coord quaternion qy
# type: double
# default: 0.0
--imu_vehicle_qy=0.0

# vehicle coord to imu coord quaternion qz
# type: double
# default: 0.0
--imu_vehicle_qz=0.0

# vehicle coord to imu coord quaternion qw
# type: double
# default: 1.0
--imu_vehicle_qw=1.0

# The tem folder for localziation visualziation
--map_visual_dir=data/map_visual

# if use avx to accelerate lidar localization
# need cpu to support avx intel intrinsics
# default: false
--if_use_avx=true
