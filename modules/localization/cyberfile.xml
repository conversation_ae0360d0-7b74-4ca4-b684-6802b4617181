<package format="2">
  <name>localization</name>
  <version>local</version>
  <description>
    This module provides localization services. There are two ways in which localization is provided:
    -  The RTK (Real Time Kinematic) based method which incorporates GPS and IMU (Inertial Measurement Unit) information
    - The  multi-sensor fusion method which incorporates GPS, IMU, and LiDAR information.
  </description>

  <maintainer email="<EMAIL>">Apollo</maintainer>
  <license>Apache License 2.0</license>
  <url type="website">https://www.apollo.auto/</url>
  <url type="repository">https://github.com/ApolloAuto/apollo</url>
  <url type="bugtracker">https://github.com/ApolloAuto/apollo/issues</url>
  <depend repo_name="com_github_gflags_gflags" lib_names="gflags">3rd-gflags</depend>
  <depend repo_name="com_github_google_glog" lib_names="glog">3rd-glog</depend>
  <depend repo_name="com_github_jbeder_yaml_cpp" lib_names="yaml-cpp">3rd-yaml-cpp</depend>
  <depend repo_name="boost">3rd-boost</depend>
  <depend repo_name="com_google_googletest" lib_names="gtest,gtest_main">3rd-gtest</depend>
  <depend expose="False">3rd-pcl</depend>
  <depend repo_name="eigen">3rd-eigen3</depend>
  <depend repo_name="com_google_absl" lib_names="absl">3rd-absl</depend>
  <depend repo_name="proj">3rd-proj</depend>
  <depend repo_name="opencv" lib_names="core,highgui,imgproc,imgcodecs">3rd-opencv</depend>
  <depend type="binary" repo_name="common" lib_names="common">common</depend>
  <depend type="binary" repo_name="perception-common" lib_names="perception-common">perception-common</depend>
  <depend type="binary" repo_name="common-msgs" lib_names="common-msgs">common-msgs</depend>
  <depend type="binary" repo_name="cyber">cyber</depend>
  <depend type="binary" repo_name="transform" lib_names="transform">transform</depend>
  <depend expose="False">3rd-localization-msf</depend>


  <depend expose="False">3rd-rules-python</depend>
  <depend expose="False">3rd-grpc</depend>
  <depend expose="False">3rd-bazel-skylib</depend>
  <depend expose="False">3rd-rules-proto</depend>
  <depend expose="False">3rd-py</depend>

  <type>module</type>
  <src_path url="https://github.com/ApolloAuto/apollo">//modules/localization</src_path>

</package>
