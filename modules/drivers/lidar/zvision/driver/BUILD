load("//tools:apollo_package.bzl", "apollo_package", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component")
load("//tools:cpplint.bzl", "cpplint")

package(default_visibility = ["//visibility:public"])

ZVISION_COPTS = ["-DMODULE_NAME=\\\"zvision\\\""]

apollo_component(
    name = "libzvision_driver_component.so",
    deps = [":zvision_driver_component_lib"],
)

apollo_cc_library(
    name = "zvision_driver_component_lib",
    srcs = ["zvision_driver_component.cc"],
    hdrs = ["zvision_driver_component.h"],
    copts = ZVISION_COPTS,
    linkopts = select(
        {
            "@platforms//cpu:aarch64": [
                "-L/usr/lib/aarch64-linux-gnu/tegra/",
                "-lnvbufsurface",
                "-lnvbufsurftransform",
                "-L/usr/lib/aarch64-linux-gnu/",
                "-lv4l2",
            ],
            "@platforms//cpu:x86_64": [],
        },
    ),
    deps = [
        "//cyber",
        "//modules/common/util:message_util",
        "//modules/drivers/lidar/zvision/parser:convert",
        #"//modules/drivers/lidar/proto:config",
        ":driver",
    ],
)

apollo_cc_library(
    name = "driver",
    srcs = [
        "zvision_driver.cc",
        "socket_input.cc",
    ],
    hdrs = [
        "zvision_driver.h",
        "input.h",
        "socket_input.h",
    ],
    copts = ZVISION_COPTS,
    deps = [
        "//cyber",
        "//modules/common/util:message_util",
        "//modules/drivers/lidar/common/driver_factory:apollo_lidar_driver_base",
        #"//modules/drivers/lidar/proto:config",
        "//modules/drivers/lidar/zvision/proto:zvision_config_proto",
    ],
)

apollo_package()

cpplint()
