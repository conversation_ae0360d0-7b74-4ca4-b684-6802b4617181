#module_config {
#    module_library : "modules/transform/libstatic_transform_component.so"
#    components {
#        class_name : "StaticTransformComponent"
#        config {
#            name : "static_transform"
#            config_file_path: "modules/transform/conf/static_transform_conf.pb.txt"
#        }
#    }
#}

###################################################
##                   convert                      #
###################################################
#module_config {
#    module_library : "modules/drivers/lidar/zvision/parser/libzvision_convert_component.so"
#    
#    components {
#      class_name : "ZvisionConvertComponent"
#      config {
#        name : "zvision_ml30_convert_sensor_left"
#        config_file_path : "modules/drivers/lidar/zvision/conf/zvision_ml30sa1_sensor_left.pb.txt"
#        readers {
#          channel: "/apollo/sensor/zvision_ml30sa1/left/Scan"
#          pending_queue_size: 10
#        }
#      }
#    }
#    
#    components {
#      class_name : "ZvisionConvertComponent"
#      config {
#        name : "zvision_ml30_convert_sensor_right"
#        config_file_path : "modules/drivers/lidar/zvision/conf/zvision_ml30sa1_sensor_right.pb.txt"
#        readers {
#          channel: "/apollo/sensor/zvision_ml30sa1/right/Scan"
#          pending_queue_size: 10
#        }
#      }
#    }
#
#    components {
#      class_name : "ZvisionConvertComponent"
#      config {
#        name : "zvision_ml30_convert_sensor_front"
#        config_file_path : "modules/drivers/lidar/zvision/conf/zvision_ml30sa1_sensor_front.pb.txt"
#        readers {
#          channel: "/apollo/sensor/zvision_ml30sa1/front/Scan"
#          pending_queue_size: 10
#        }
#      }
#    }
#}



##################################################
#                   fusion                       #
##################################################
module_config {
    module_library : "modules/drivers/lidar/zvision/fusion/libzvision_fusion_component.so"

    components {
      class_name : "ZvisionFusionComponent"
      config {
        name : "zvision_fusion"
        config_file_path : "modules/drivers/lidar/zvision/conf/zvision_fusion_conf.pb.txt"
        flag_file_path: "modules/perception/data/flag/perception_common.flag"
        readers {
          channel: "/apollo/sensor/zvision_ml30sa1/PointCloud2"
          pending_queue_size: 30
        }
      }
    }
}

