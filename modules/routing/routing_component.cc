/******************************************************************************
 * Copyright 2022 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "modules/routing/routing_component.h"

#include <string>
#include <unordered_set>
#include <utility>

#include "modules/common/adapters/adapter_gflags.h"
#include "modules/map/hdmap/hdmap_util.h"
#include "modules/routing/common/routing_gflags.h"

namespace apollo {
namespace routing {

using apollo::common::ErrorCode;
using apollo::common::PointENU;
using apollo::common::Status;
using apollo::common::math::Box2d;
using apollo::common::math::Polygon2d;
using apollo::common::math::Vec2d;
using apollo::cyber::Clock;
using apollo::hdmap::HDMapUtil;
using apollo::hdmap::Junction;
using apollo::hdmap::JunctionInfoConstPtr;
using apollo::hdmap::Lane;
using apollo::hdmap::LaneBoundary;
using apollo::hdmap::LaneBoundaryType;
using apollo::hdmap::LaneInfoConstPtr;
using apollo::routing::LaneWaypoint;

namespace {
constexpr double kStartSBuffer = 2.0;                // m
constexpr double kEndSBuffer = 10.0;                 // m
constexpr double kUturnStartSBuffer = 20.0;          // m
constexpr double kUturnEndSBuffer = 1.0;             // m
constexpr double kUturnTargetSBuffer = 5.0;          // m
constexpr double kProcessRequestIntervalTime = 5.0;  // s
constexpr int kIsEmptyIntValue = 0;                  // int size
}  // namespace

bool RoutingComponent::Init() {
  ACHECK(cyber::ComponentBase::GetProtoConfig(&routing_config_))
      << "Unable to load routing conf file: "
      << cyber::ComponentBase::ConfigFilePath();

  AINFO << "Config file: " << cyber::ComponentBase::ConfigFilePath()
        << " is loaded.";

  planning_reader_ = node_->CreateReader<planning::ADCTrajectory>(
      routing_config_.topic_config().planning_trajectory_topic(),
      [this](
          const std::shared_ptr<planning::ADCTrajectory> &planning_trajectory) {
        std::lock_guard<std::mutex> lock(mutex_);
        planning_trajectory_.CopyFrom(*planning_trajectory);
        has_reached_destination_ =
            planning_trajectory->has_reached_destination();
      });

  apollo::cyber::proto::RoleAttributes attr;
  attr.set_channel_name(
      routing_config_.topic_config().routing_response_topic());
  auto qos = attr.mutable_qos_profile();
  qos->set_history(apollo::cyber::proto::QosHistoryPolicy::HISTORY_KEEP_LAST);
  qos->set_reliability(
      apollo::cyber::proto::QosReliabilityPolicy::RELIABILITY_RELIABLE);
  qos->set_durability(
      apollo::cyber::proto::QosDurabilityPolicy::DURABILITY_TRANSIENT_LOCAL);
  response_writer_ = node_->CreateWriter<RoutingResponse>(attr);

  apollo::cyber::proto::RoleAttributes attr_history;
  attr_history.set_channel_name(
      routing_config_.topic_config().routing_response_history_topic());
  auto qos_history = attr_history.mutable_qos_profile();
  qos_history->set_history(
      apollo::cyber::proto::QosHistoryPolicy::HISTORY_KEEP_LAST);
  qos_history->set_reliability(
      apollo::cyber::proto::QosReliabilityPolicy::RELIABILITY_RELIABLE);
  qos_history->set_durability(
      apollo::cyber::proto::QosDurabilityPolicy::DURABILITY_TRANSIENT_LOCAL);

  response_history_writer_ = node_->CreateWriter<RoutingResponse>(attr_history);

  routing_result_writer_ = node_->CreateWriter<RoutingResult>(
      routing_config_.topic_config().routing_result_topic());
  std::weak_ptr<RoutingComponent> self =
      std::dynamic_pointer_cast<RoutingComponent>(shared_from_this());
  timer_.reset(new ::apollo::cyber::Timer(
      FLAGS_routing_response_history_interval_ms,
      [self, this]() {
        auto ptr = self.lock();
        if (ptr) {
          std::lock_guard<std::mutex> guard(this->mutex_);
          if (this->response_ != nullptr) {
            auto timestamp = apollo::cyber::Clock::NowInSeconds();
            response_->mutable_header()->set_timestamp_sec(timestamp);
            this->response_history_writer_->Write(*response_);
          }
        }
      },
      false));
  timer_->Start();

  request_timer_.reset(new ::apollo::cyber::Timer(
      FLAGS_routing_request_interval_ms,
      [self, this]() {
        auto ptr = self.lock();
        if (ptr) {
          ProcessNextRequest();
        }
      },
      false));

  hdmap_ = apollo::hdmap::HDMapUtil::BaseMapPtr();
  ACHECK(hdmap_) << "Failed to load map file:" << apollo::hdmap::BaseMapFile();

  return routing_.Init().ok() && routing_.Start().ok();
}

void RoutingComponent::ProcessNextRequest() {
  ADEBUG << "reached_destination: " << has_reached_destination_;
  ADEBUG << "need_process_now: " << need_process_now_;
  if (has_reached_destination_ && need_process_now_) {
    ADEBUG << "ProcessNextRequest now.";
    need_process_now_ = false;
    last_process_time_ = Clock::NowInSeconds();
    if (!routing_requests_.empty()) {
      RoutingRequest first_request = routing_requests_.front();
      auto timestamp = Clock::NowInSeconds();
      first_request.mutable_header()->set_timestamp_sec(timestamp);
      routing_requests_.erase(routing_requests_.begin());
      ADEBUG << "Try ProcessNextRequest.";
      auto response = std::make_shared<RoutingResponse>();
      const std::shared_ptr<RoutingRequest> &request =
          std::make_shared<RoutingRequest>(first_request);
      ADEBUG << "request:\n" << request->DebugString();
      bool succeed = routing_.Process(request, response.get());
      routing_result_.mutable_header()->set_timestamp_sec(timestamp);
      routing_result_.set_routing_result(succeed);
      routing_result_.set_from_cloud(true);
      routing_result_writer_->Write(routing_result_);
      if (succeed) {
        ADEBUG << "response:\n" << response->DebugString();
        common::util::FillHeader(node_->Name(), response.get());
        response_writer_->Write(response);
        {
          std::lock_guard<std::mutex> guard(mutex_);
          response_ = std::move(response);
        }
      } else {
        AERROR << "Try ProcessNextRequest failed.";
      }
    } else {
      AERROR << "ProcessNextRequest is empty, so failed.";
      request_timer_->Stop();
    }
  }

  if (!need_process_now_) {
    if (Clock::NowInSeconds() - last_process_time_ >
        kProcessRequestIntervalTime) {
      need_process_now_ = true;
      AERROR << "need_process_now_ is now true.";
    }
  }
}

bool RoutingComponent::ProcessCurrentRequest() {
  if (!routing_requests_.empty()) {
    need_process_now_ = false;
    last_process_time_ = Clock::NowInSeconds();

    RoutingRequest first_request = routing_requests_.front();
    auto timestamp = Clock::NowInSeconds();
    first_request.mutable_header()->set_timestamp_sec(timestamp);
    routing_requests_.erase(routing_requests_.begin());

    ADEBUG << "Try ProcessCurrentRequest.";
    auto response = std::make_shared<RoutingResponse>();
    const std::shared_ptr<RoutingRequest> &request =
        std::make_shared<RoutingRequest>(first_request);
    ADEBUG << "request:\n" << request->DebugString();
    bool succeed = routing_.Process(request, response.get());
    routing_result_.mutable_header()->set_timestamp_sec(timestamp);
    routing_result_.set_routing_result(succeed);
    routing_result_.set_from_cloud(true);
    routing_result_writer_->Write(routing_result_);
    if (succeed) {
      ADEBUG << "response:\n" << response->DebugString();
      common::util::FillHeader(node_->Name(), response.get());
      response_writer_->Write(response);
      {
        std::lock_guard<std::mutex> guard(mutex_);
        response_ = std::move(response);
      }
      return true;
    } else {
      AERROR << "ProcessCurrentRequest failed.";
      return false;
    }
  }

  AERROR << "ProcessCurrentRequest is empty, so failed.";
  return false;
}

bool RoutingComponent::Proc(const std::shared_ptr<RoutingRequest> &request) {
  auto timestamp = Clock::NowInSeconds();
  routing_result_.mutable_header()->set_timestamp_sec(timestamp);
  if (request->has_from_cloud() && request->from_cloud()) {
    AINFO <<timestamp << "Proc receive cloud_request.";
    bool cloud_request = ProcessCloudRequest(request);
    routing_result_.set_routing_result(cloud_request);
    routing_result_.set_from_cloud(true);
    routing_result_writer_->Write(routing_result_);
    return cloud_request;
  } else {
    AINFO <<timestamp << "Proc receive local_request.";
    bool local_request = ProcessLocalRequest(request);
    routing_result_.set_routing_result(local_request);
    routing_result_.set_from_cloud(false);
    routing_result_writer_->Write(routing_result_);
    return local_request;
  }
}

bool RoutingComponent::ProcessLocalRequest(
    const std::shared_ptr<RoutingRequest> &request) {
  auto response = std::make_shared<RoutingResponse>();
  if (!routing_.Process(request, response.get())) {
    AERROR << "ProcessLocalRequest failed, request is: \n"
           << request->DebugString();
    return false;
  }
  if (FLAGS_enable_extend_passage) {
    if (kIsEmptyIntValue == response.get()->road_size()) {
      AERROR << "response.get()->road_size() == 0 | ProcessLocalRequest "
                "failed, request is: \n"
             << request->DebugString();
      return false;
    } else {
      if (!ExtendPassage(response.get())) {
        AERROR << "ExtendPassage failed, request is: \n"
               << request->DebugString();
        return false;
      }
    }
  }

  common::util::FillHeader(node_->Name(), response.get());
  response_writer_->Write(response);
  {
    std::lock_guard<std::mutex> guard(mutex_);
    response_ = std::move(response);
  }
  return true;
}

bool RoutingComponent::ProcessCloudRequest(
    const std::shared_ptr<RoutingRequest> &request) {
  auto response = std::make_shared<RoutingResponse>();
  request_timer_->Stop();
  routing_requests_.clear();

  // 1.first try process request
  if (routing_.Process(request, response.get())) {
    if (FLAGS_enable_extend_passage) {
      if (kIsEmptyIntValue == response.get()->road_size()) {
        AERROR << "response.get()->road_size() == 0 | ProcessLocalRequest "
                  "failed, request is: \n"
               << request->DebugString();
        return false;
      } else {
        if (!ExtendPassage(response.get())) {
          AERROR << "ExtendPassage failed, request is: \n"
                 << request->DebugString();
          return false;
        }
      }
    }

    common::util::FillHeader(node_->Name(), response.get());
    response_writer_->Write(response);
    {
      std::lock_guard<std::mutex> guard(mutex_);
      response_ = std::move(response);
    }
    return true;
  }

  AERROR << "First try ProcessCloudRequest failed, request is: \n"
         << request->DebugString();

  // 2.get dead end info
  if (!GetDeadEndInfo()) {
    AERROR << "GetDeadEndInfo failed.";
    return false;
  }

  // 3.generate request list
  if (!GenerateRequests(request)) {
    AERROR << "GenerateRequests failed.";
    return false;
  }

  // 4.process first request
  ProcessCurrentRequest();

  // 5.timer start
  request_timer_->Start();

  return true;
}

bool RoutingComponent::GetDeadEndInfo() {
  hdmap::Map map;
  bool load_succeed =
      cyber::common::GetProtoFromFile(apollo::hdmap::BaseMapFile(), &map);
  dead_end_vertices_.clear();
  dead_end_junction_.Clear();
  if (load_succeed) {
    int num = map.junction_size();
    if (num <= 0) {
      const std::string msg = "No junctions from map";
      AERROR << msg;
      return false;
    }

    // TODO(swh): Need to consider the situation with multiple
    // Junction::DEAD_END.
    for (const auto &junction : map.junction()) {
      if (Junction::DEAD_END == junction.type()) {
        dead_end_junction_.CopyFrom(junction);
        auto &junction_point = junction.polygon().point();
        AINFO << "junction_point size is " << junction_point.size();
        for (int i = 0; i < junction_point.size(); ++i) {
          dead_end_vertices_.emplace_back(junction_point.at(i).x(),
                                          junction_point.at(i).y());
        }
        break;
      }
    }

    ADEBUG << "dead_end_junction_ is: \n" << dead_end_junction_.DebugString();
    if (dead_end_vertices_.empty()) {
      AERROR << "no dead_end from junctions";
      return false;
    }
  } else {
    AERROR << "hdmap::BaseMapFile is load failed: "
           << apollo::hdmap::BaseMapFile();
    return false;
  }

  return true;
}

bool RoutingComponent::CalLane(apollo::hdmap::LaneInfoConstPtr *right_lane,
                               apollo::hdmap::LaneInfoConstPtr *left_lane) {
  auto first_point = dead_end_vertices_.front();
  auto last_point = dead_end_vertices_.back();
  double vehicle_lane_s = 0.0;
  double vehicle_lane_l = 0.0;
  PointENU right_car_pose;
  PointENU left_car_pose;

  right_car_pose.set_x(first_point.x());
  right_car_pose.set_y(first_point.y());
  hdmap_->GetNearestLane(right_car_pose, right_lane, &vehicle_lane_s,
                         &vehicle_lane_l);
  if (nullptr == right_lane->get()) {
    AERROR << "hdmap_->GetNearestLane get right lane failed with pos x: "
           << right_car_pose.x() << " y: " << right_car_pose.y();
    return false;
  }

  left_car_pose.set_x(last_point.x());
  left_car_pose.set_y(last_point.y());
  hdmap_->GetNearestLane(left_car_pose, left_lane, &vehicle_lane_s,
                         &vehicle_lane_l);
  if (nullptr == left_lane->get()) {
    AERROR << "hdmap_->GetNearestLane get left lane failed with pos x: "
           << left_car_pose.x() << " y: " << left_car_pose.y();
    return false;
  }
  return true;
}

void RoutingComponent::FillRoutingRequest(
    const bool first_bool, const LocalRoutingType routing_type,
    const double *timestamp, const std::string str, const uint32_t *seq_num,
    const LaneWaypoint *first_waypoint, const LaneWaypoint *second_waypoint,
    RoutingRequest *const routing_request) {
  routing_request->set_final_route(first_bool);
  routing_request->set_local_routing_type(routing_type);
  routing_request->mutable_header()->set_timestamp_sec(*timestamp);
  routing_request->mutable_header()->set_module_name(str);
  routing_request->mutable_header()->set_sequence_num(*seq_num);
  routing_request->add_waypoint()->CopyFrom(*first_waypoint);
  routing_request->add_waypoint()->CopyFrom(*second_waypoint);
  return;
}

bool RoutingComponent::GenerateRequests(
    const std::shared_ptr<RoutingRequest> &request) {
  routing_requests_.clear();
  size_t waypoint_num = request->waypoint().size();
  if (waypoint_num < 2) {
    // at least two points. The first is start point, the end is final point.
    // The routing must go through each point in waypoint.
    AERROR << "request waypoint size is less than 2 points, is: "
           << waypoint_num;
    return false;
  }

  LaneInfoConstPtr right_lane;  LaneInfoConstPtr left_lane;
  if (!CalLane(&right_lane, &left_lane)) {
    return false;
  }

  // 1.first request:lane follow
  // Get the routing endpoint of the right lane
  PointENU routing_end_pose;
  double length = right_lane->total_length();
  // ADEBUG << "right_lane id: " << right_lane->lane().id().id()
  //        << " length: " << length;
  double s = length - routing_config_.generate_config().end_s_buffer();
  routing_end_pose = right_lane->GetSmoothPoint(s);
  // ADEBUG << "right_lane end_pose: " << routing_end_pose.DebugString();

  // whether to generate the first routing request
  bool generate_first_routing =
      routing_config_.generate_config().generate_first_routing();

  RoutingRequest routing_request;
  double timestamp = apollo::cyber::Clock::NowInSeconds();
  const uint32_t seq_num = static_cast<uint32_t>(seq_num_++);
  LaneWaypoint first_waypoint;  LaneWaypoint second_waypoint;
  first_waypoint.mutable_pose()->CopyFrom(request->waypoint(0).pose());
  second_waypoint.mutable_pose()->CopyFrom(routing_end_pose);
  FillRoutingRequest(false, ROUTING_INSIDE, &timestamp, "from_cloud first",
                     &seq_num, &first_waypoint, &second_waypoint,
                     &routing_request);

  if (generate_first_routing) {
    routing_requests_.emplace_back(routing_request);
  }
  // ADEBUG << "routing_request 1:\n" << routing_request.DebugString();

  // 2.second request:dead end
  // Get the route starting point of the right lane
  PointENU routing_start_pose;
  s = length - routing_config_.generate_config().uturn_start_s_buffer();
  routing_start_pose = right_lane->GetSmoothPoint(s);
  // ADEBUG << "routing_start_pose: " << routing_start_pose.DebugString();

  s = length - routing_config_.generate_config().uturn_end_s_buffer();
  routing_end_pose = right_lane->GetSmoothPoint(s);
  // ADEBUG << "routing_end_pose: " << routing_end_pose.DebugString();

  routing_request.Clear();
  timestamp = apollo::cyber::Clock::NowInSeconds();
  const uint32_t seq_num2 = static_cast<uint32_t>(seq_num_++);

  first_waypoint.Clear();  second_waypoint.Clear();
  if (generate_first_routing) {
    first_waypoint.mutable_pose()->CopyFrom(routing_start_pose);
  } else {
    first_waypoint.mutable_pose()->CopyFrom(request->waypoint(0).pose());
  }
  second_waypoint.mutable_pose()->CopyFrom(routing_end_pose);
  FillRoutingRequest(false, ROUTING_INSIDE, &timestamp, "from_cloud second",
                     &seq_num2, &first_waypoint, &second_waypoint,
                     &routing_request);

  // dead_end_info
  PointENU routing_target_pose;
  s = routing_config_.generate_config().uturn_target_s_buffer();
  routing_target_pose = left_lane->GetSmoothPoint(s);
  routing_request.mutable_dead_end_info()->set_dead_end_routing_type(
      ROUTING_IN);
  routing_request.mutable_dead_end_info()->mutable_target_point()->CopyFrom(
      routing_target_pose);

  routing_requests_.emplace_back(routing_request);
  // ADEBUG << "routing_request 2:\n" << routing_request.DebugString();

  // 3.third request:lane follow
  length = left_lane->total_length();
  // ADEBUG << "left_lane id: " << left_lane->lane().id().id()
  //        << " length: " << length;
  s = routing_config_.generate_config().start_s_buffer();
  routing_start_pose = left_lane->GetSmoothPoint(s);
  // ADEBUG << "left_lane routing_start_pose: "
  //        << routing_start_pose.DebugString();

  routing_request.Clear();  first_waypoint.Clear();  second_waypoint.Clear();
  timestamp = apollo::cyber::Clock::NowInSeconds();
  const uint32_t seq_num3 = static_cast<uint32_t>(seq_num_++);

  first_waypoint.mutable_pose()->CopyFrom(routing_start_pose);
  second_waypoint.mutable_pose()->CopyFrom(
      request->waypoint(waypoint_num - 1).pose());
  FillRoutingRequest(true, ROUTING_INSIDE, &timestamp, "from_cloud third",
                     &seq_num3, &first_waypoint, &second_waypoint,
                     &routing_request);

  routing_requests_.emplace_back(routing_request);
  // ADEBUG << "routing_request 3:\n" << routing_request.DebugString();

  return true;
}

// bool RoutingComponent::GenerateRequests(
//     const std::shared_ptr<RoutingRequest> &request) {
//   routing_requests_.clear();
//   size_t waypoint_num = request->waypoint().size();
//   if (waypoint_num < 2) {
//     // at least two points. The first is start point, the end is final point.
//     // The routing must go through each point in waypoint.
//     AERROR << "request waypoint size is less than 2 points, is: "
//            << waypoint_num;
//     return false;
//   }
//   auto first_point = dead_end_vertices_.front();
//   auto last_point = dead_end_vertices_.back();
//   double vehicle_lane_s = 0.0;
//   double vehicle_lane_l = 0.0;
//   LaneInfoConstPtr right_lane;
//   LaneInfoConstPtr left_lane;
//   PointENU right_car_pose;
//   PointENU left_car_pose;

//   right_car_pose.set_x(first_point.x());
//   right_car_pose.set_y(first_point.y());
//   hdmap_->GetNearestLane(right_car_pose, &right_lane, &vehicle_lane_s,
//                          &vehicle_lane_l);
//   if (nullptr == right_lane.get()) {
//     AERROR << "hdmap_->GetNearestLane get right lane failed with pos x: "
//            << right_car_pose.x() << " y: " << right_car_pose.y();
//     return false;
//   }

//   left_car_pose.set_x(last_point.x());
//   left_car_pose.set_y(last_point.y());
//   hdmap_->GetNearestLane(left_car_pose, &left_lane, &vehicle_lane_s,
//                          &vehicle_lane_l);
//   if (nullptr == left_lane.get()) {
//     AERROR << "hdmap_->GetNearestLane get left lane failed with pos x: "
//            << left_car_pose.x() << " y: " << left_car_pose.y();
//     return false;
//   }

//   // 1.first request:lane follow
//   // Get the routing endpoint of the right lane
//   PointENU routing_end_pose;
//   double length = right_lane->total_length();
//   ADEBUG << "right_lane id: " << right_lane->lane().id().id()
//          << " length: " << length;
//   double s = length - routing_config_.generate_config().end_s_buffer();
//   routing_end_pose = right_lane->GetSmoothPoint(s);
//   ADEBUG << "right_lane routing_end_pose: " <<
//   routing_end_pose.DebugString();

//   // whether to generate the first routing request
//   bool generate_first_routing = true;
//   generate_first_routing =
//       routing_config_.generate_config().generate_first_routing();

//   RoutingRequest routing_request;
//   routing_request.set_final_route(false);
//   routing_request.set_local_routing_type(ROUTING_INSIDE);
//   double timestamp = apollo::cyber::Clock::NowInSeconds();
//   routing_request.mutable_header()->set_timestamp_sec(timestamp);
//   routing_request.mutable_header()->set_module_name("from_cloud first");
//   const uint32_t seq_num = static_cast<uint32_t>(seq_num_++);
//   routing_request.mutable_header()->set_sequence_num(seq_num);

//   LaneWaypoint first_waypoint;
//   first_waypoint.mutable_pose()->CopyFrom(request->waypoint(0).pose());
//   LaneWaypoint second_waypoint;
//   second_waypoint.mutable_pose()->CopyFrom(routing_end_pose);

//   routing_request.add_waypoint()->CopyFrom(first_waypoint);
//   routing_request.add_waypoint()->CopyFrom(second_waypoint);

//   if (generate_first_routing) {
//     routing_requests_.emplace_back(routing_request);
//   }
//   ADEBUG << "routing_request 1:\n" << routing_request.DebugString();

//   // 2.second request:dead end
//   // Get the route starting point of the right lane
//   PointENU routing_start_pose;
//   s = length - routing_config_.generate_config().uturn_start_s_buffer();
//   routing_start_pose = right_lane->GetSmoothPoint(s);
//   ADEBUG << "routing_start_pose: " << routing_start_pose.DebugString();

//   s = length - routing_config_.generate_config().uturn_end_s_buffer();
//   routing_end_pose = right_lane->GetSmoothPoint(s);
//   ADEBUG << "routing_end_pose: " << routing_end_pose.DebugString();

//   routing_request.Clear();
//   routing_request.set_final_route(false);
//   routing_request.set_local_routing_type(ROUTING_INSIDE);
//   timestamp = apollo::cyber::Clock::NowInSeconds();
//   routing_request.mutable_header()->set_timestamp_sec(timestamp);
//   routing_request.mutable_header()->set_module_name("from_cloud second");
//   const uint32_t seq_num2 = static_cast<uint32_t>(seq_num_++);
//   routing_request.mutable_header()->set_sequence_num(seq_num2);

//   first_waypoint.Clear();
//   if (generate_first_routing) {
//     first_waypoint.mutable_pose()->CopyFrom(routing_start_pose);
//   } else {
//     first_waypoint.mutable_pose()->CopyFrom(request->waypoint(0).pose());
//   }
//   second_waypoint.Clear();
//   second_waypoint.mutable_pose()->CopyFrom(routing_end_pose);

//   routing_request.add_waypoint()->CopyFrom(first_waypoint);
//   routing_request.add_waypoint()->CopyFrom(second_waypoint);

//   // dead_end_info
//   PointENU routing_target_pose;
//   s = routing_config_.generate_config().uturn_target_s_buffer();
//   routing_target_pose = left_lane->GetSmoothPoint(s);
//   routing_request.mutable_dead_end_info()->set_dead_end_routing_type(
//       ROUTING_IN);
//   routing_request.mutable_dead_end_info()->mutable_target_point()->CopyFrom(
//       routing_target_pose);

//   routing_requests_.emplace_back(routing_request);
//   ADEBUG << "routing_request 2:\n" << routing_request.DebugString();

//   // 3.third request:lane follow
//   length = left_lane->total_length();
//   ADEBUG << "left_lane id: " << left_lane->lane().id().id()
//          << " length: " << length;
//   s = routing_config_.generate_config().start_s_buffer();
//   routing_start_pose = left_lane->GetSmoothPoint(s);
//   ADEBUG << "left_lane routing_start_pose: "
//          << routing_start_pose.DebugString();

//   routing_request.Clear();
//   routing_request.set_final_route(true);
//   routing_request.set_local_routing_type(ROUTING_INSIDE);
//   timestamp = apollo::cyber::Clock::NowInSeconds();
//   routing_request.mutable_header()->set_timestamp_sec(timestamp);
//   routing_request.mutable_header()->set_module_name("from_cloud third");
//   const uint32_t seq_num3 = static_cast<uint32_t>(seq_num_++);
//   routing_request.mutable_header()->set_sequence_num(seq_num3);

//   first_waypoint.Clear();
//   first_waypoint.mutable_pose()->CopyFrom(routing_start_pose);
//   second_waypoint.Clear();
//   second_waypoint.mutable_pose()->CopyFrom(
//       request->waypoint(waypoint_num - 1).pose());

//   routing_request.add_waypoint()->CopyFrom(first_waypoint);
//   routing_request.add_waypoint()->CopyFrom(second_waypoint);

//   routing_requests_.emplace_back(routing_request);
//   ADEBUG << "routing_request 3:\n" << routing_request.DebugString();

//   return true;
// }

bool RoutingComponent::ExtendPassage(routing::RoutingResponse *routing) {
  std::unordered_set<std::string> left_neighbor_lanes;
  std::pair<std::string, std::string> end_left_and_right_lane;
  std::string start_lane_id;
  std::string end_lane_id;

  if (routing == nullptr) {
    AERROR << "routing == nullptr";
    return false;
  }
  if (kIsEmptyIntValue == routing->road_size()) {
    AERROR << "routing->road_size()== 0";
    return false;
  }

  std::unordered_set<std::string> all_routing_lanes_id;
  for (int road_index = 0; road_index < routing->road_size(); ++road_index) {
    auto road_segment = routing->mutable_road(road_index);
    ADEBUG << "road_segment->passage_size()==" << road_segment->passage_size();
    for (int passage_index = 0; passage_index < road_segment->passage_size();
         ++passage_index) {
      auto passage = road_segment->passage(passage_index);
      ADEBUG << "passage.segment_size()==" << passage.segment_size();
      for (int lane_index = 0; lane_index < passage.segment_size();
           ++lane_index) {
        all_routing_lanes_id.emplace(passage.segment(lane_index).id());
        if (0 == road_index && 0 == passage_index && 0 == lane_index) {
          start_lane_id = passage.segment(0).id();
          ADEBUG << "start_lane_id==" << start_lane_id;
        }
        if (road_index == routing->road_size() - 1 &&
            passage_index == road_segment->passage_size() - 1 &&
            lane_index == passage.segment_size() - 1) {
          end_lane_id = passage.segment(lane_index).id();
          ADEBUG << "end_lane_id==" << end_lane_id;
        }
      }
    }
  }

  if (!GetEndLane(end_lane_id, &end_left_and_right_lane)) {
    return false;
  }

  if (!ProcessLeftLanes(start_lane_id, end_lane_id, routing,
                        all_routing_lanes_id, end_left_and_right_lane)) {
    return false;
  }

  return true;
}

bool RoutingComponent::GetEndLane(
    const std::string &end_lane_id,
    std::pair<std::string, std::string> *end_left_and_right_lane) {
  auto end_lane = hdmap_->GetLaneById(hdmap::MakeMapId(end_lane_id));
  if (end_lane == nullptr) {
    AERROR << "end_lane == nullptr";
    return false;
  }

  auto end_lane_right_neighbor_ids =
      end_lane->lane().right_neighbor_forward_lane_id();
  if (end_lane_right_neighbor_ids.empty()) {
    ADEBUG << "end_lane_right_neighbor_ids is empty.";
  } else {
    const auto &end_lane_right_neighbor_id =
        end_lane_right_neighbor_ids.at(0).id();
    end_left_and_right_lane->second = end_lane_right_neighbor_id;
    ADEBUG << "end_lane_right_neighbor_id=" << end_lane_right_neighbor_id;
  }

  auto end_lane_left_neighbor_ids =
      end_lane->lane().left_neighbor_forward_lane_id();
  if (end_lane_left_neighbor_ids.empty()) {
    ADEBUG << "end_lane_left_neighbor_ids is empty.";
  } else {
    const auto &end_lane_left_neighbor_id =
        end_lane_left_neighbor_ids.at(0).id();
    end_left_and_right_lane->first = end_lane_left_neighbor_id;
    ADEBUG << "end_lane_left_neighbor_id=" << end_lane_left_neighbor_id;
  }

  return true;
}

apollo::routing::Passage RoutingComponent::SetLeftPassage(
    const std::string &left_lane_id,
    const std::unordered_set<std::string> &all_routing_lanes_id,
    bool neight_end_lane, bool *const neight_routing) {
  apollo::routing::Passage left_passage;
  auto left_lane = hdmap_->GetLaneById(hdmap::MakeMapId(left_lane_id));
  auto left_lane_successor = left_lane->lane().successor_id();
  if (left_lane->lane().successor_id().empty()) {
    left_passage.set_can_exit(false);
    left_passage.set_change_lane_type(routing::RIGHT);
  } else {
    for (auto iter = left_lane_successor.begin();
         iter != left_lane_successor.end(); ++iter) {
      auto left_lane_successor_right_neighbor =
          hdmap_->GetLaneById(hdmap::MakeMapId(iter->id()));
      auto left_lane_successor_right_neighbor_ids =
          left_lane_successor_right_neighbor->lane()
              .right_neighbor_forward_lane_id();
      if (left_lane_successor_right_neighbor_ids.empty()) {
        ADEBUG << "left_lane_successor_right_neighbor_ids is empty.";
        continue;
      }
      const auto &left_lane_successor_right_neighbor_id =
          left_lane_successor_right_neighbor_ids.at(0).id();

      for (auto iter_all = all_routing_lanes_id.begin();
           iter_all != all_routing_lanes_id.end(); ++iter_all) {
        if (iter_all->data() == left_lane_successor_right_neighbor_id) {
          ADEBUG << "left_lane_successor_right_neighbor_id="
                 << left_lane_successor_right_neighbor_id;

          auto left_lane_successor_right_neighbor = hdmap_->GetLaneById(
              hdmap::MakeMapId(left_lane_successor_right_neighbor_id));
          bool next_lane_is_virtual = left_lane_successor_right_neighbor->lane()
                                          .left_boundary()
                                          .virtual_();
          auto next_lane_left_boundary_type =
              left_lane_successor_right_neighbor->lane()
                  .left_boundary()
                  .boundary_type(0)
                  .types(0);
          if (FLAGS_enable_extend_passage_junction) {
            *neight_routing = true;
            break;
          } else if (!next_lane_is_virtual &&
                     LaneBoundaryType::DOTTED_WHITE ==
                         next_lane_left_boundary_type) {
            *neight_routing = true;
            break;
          }
        }
      }
      if (*neight_routing) {
        break;
      }
    }
    if (*neight_routing && !neight_end_lane) {
      left_passage.set_can_exit(true);
      left_passage.set_change_lane_type(routing::FORWARD);
    } else {
      left_passage.set_can_exit(false);
      left_passage.set_change_lane_type(routing::RIGHT);
    }
  }
  return left_passage;
}

apollo::routing::LaneSegment RoutingComponent::SetLaneSegment(
    const std::string &start_lane_id, const std::string &end_lane_id,
    const Passage &passage, const routing::RoutingResponse *routing,
    apollo::hdmap::LaneInfoConstPtr left_lane) {
  apollo::routing::LaneSegment left_lane_segment;
  left_lane_segment.set_id(left_lane->id().id());

  if (0 ==
      std::strcmp(start_lane_id.c_str(), passage.segment(0).id().c_str())) {
    const auto &start_pose =
        routing->routing_request().waypoint().begin()->pose();
    double s = 0.0;
    double l = 0.0;
    if (!left_lane->GetProjection({start_pose.x(), start_pose.y()}, &s, &l)) {
      left_lane_segment.set_start_s(
          left_lane->total_length() -
          (passage.segment(0).end_s() - passage.segment(0).start_s()));
    } else {
      left_lane_segment.set_start_s(s);
    }
    left_lane_segment.set_end_s(left_lane->total_length());
  } else if (0 == std::strcmp(end_lane_id.c_str(),
                              passage.segment(0).id().c_str())) {
    left_lane_segment.set_start_s(0);
    const auto &end_pose =
        routing->routing_request().waypoint().rbegin()->pose();
    double s = 0.0;
    double l = 0.0;
    if (!left_lane->GetProjection({end_pose.x(), end_pose.y()}, &s, &l)) {
      left_lane_segment.set_end_s(passage.segment(0).end_s());
    } else {
      left_lane_segment.set_end_s(s);
    }
  } else {
    left_lane_segment.set_start_s(0);
    left_lane_segment.set_end_s(left_lane->total_length());
  }

  return left_lane_segment;
}

void RoutingComponent::LogRoutingInfo(const routing::RoutingResponse *routing) {
  for (int i = 0; i < routing->road_size(); ++i) {
    for (int j = 0; j < routing->road(i).passage_size(); ++j) {
      for (int n = 0; n < routing->road(i).passage(j).segment_size(); ++n) {
        AINFO << "road[" << i << "]<" << routing->road(i).id() << ">passage["
              << j << "]<" << routing->road(i).passage(j).can_exit() << "><"
              << routing::ChangeLaneType_Name(
                     routing->road(i).passage(j).change_lane_type())
              << ">lane[" << n << "]<"
              << routing->road(i).passage(j).segment(n).id() << "><"
              << routing->road(i).passage(j).segment(n).start_s() << "><"
              << routing->road(i).passage(j).segment(n).end_s() << ">";
      }
    }
  }
}

bool RoutingComponent::ProcessLeftLanes(
    std::string start_lane_id, std::string end_lane_id,
    routing::RoutingResponse *routing,
    const std::unordered_set<std::string> &all_routing_lanes_id,
    const std::pair<std::string, std::string> &end_left_and_right_lane) {
  for (int road_index = 0; road_index < routing->road_size(); ++road_index) {
    auto road_segment = routing->mutable_road(road_index);
    int passage_size = road_segment->passage_size();
    for (int passage_index = 0; passage_index < passage_size; ++passage_index) {
      auto passage = road_segment->passage(passage_index);
      const auto &current_lane =
          hdmap_->GetLaneById(hdmap::MakeMapId(passage.segment(0).id()));
      auto current_lane_type = current_lane->lane().type();
      bool current_lane_is_virtual =
          current_lane->lane().left_boundary().virtual_();
      auto current_lane_left_boundary_type =
          current_lane->lane().left_boundary().boundary_type(0).types(0);
      if (hdmap::Lane::PLAY_STREET == current_lane_type) {
        break;
      }

      if (!FLAGS_enable_extend_passage_junction &&
          (current_lane_is_virtual ||
           LaneBoundaryType::DOTTED_WHITE != current_lane_left_boundary_type)) {
        break;
      }
      const auto &left_ids =
          current_lane->lane().left_neighbor_forward_lane_id();

      if (left_ids.empty()) {
        ADEBUG << "left_ids is empty.";
        break;
      }

      const auto &left_lane_id = left_ids.at(0).id();
      bool in_routing_lanes = false;
      for (auto iter = all_routing_lanes_id.begin();
           iter != all_routing_lanes_id.end(); ++iter) {
        if (iter->data() == left_lane_id) {
          in_routing_lanes = true;
          break;
        }
      }
      if (in_routing_lanes) {
        continue;
      }
      bool neight_end_lane = false;
      if (left_lane_id == end_left_and_right_lane.first) {
        neight_end_lane = true;
      }

      bool neight_routing = false;
      apollo::routing::Passage left_passage = SetLeftPassage(
          left_lane_id, all_routing_lanes_id, neight_end_lane, &neight_routing);
      auto left_lane = hdmap_->GetLaneById(hdmap::MakeMapId(left_lane_id));
      apollo::routing::LaneSegment left_lane_segment = SetLaneSegment(
          start_lane_id, end_lane_id, passage, routing, left_lane);
      left_passage.add_segment()->CopyFrom(left_lane_segment);

      road_segment->add_passage()->CopyFrom(left_passage);
    }
  }

  // Update original routing direction
  UpdateOriginalRoutingPassageDirection(routing);

  // Check route change lane length
  CheckRouteChangeLaneLength(routing);

  // print routing info
  LogRoutingInfo(routing);
  return true;
}

// bool RoutingComponent::ExtendPassage(routing::RoutingResponse *routing) {
//   std::unordered_set<std::string> left_neighbor_lanes;
//   std::pair<std::string, std::string> end_left_and_right_lane;
//   std::string start_lane_id;
//   std::string end_lane_id;

//   if (routing == nullptr) {
//     AERROR << "routing == nullptr";
//     return false;
//   }
//   if (kIsEmptyIntValue == routing->road_size()) {
//     AERROR << "routing->road_size()== 0";
//     return false;
//   }

//   std::unordered_set<std::string> all_routing_lanes_id;
//   for (int road_index = 0; road_index < routing->road_size(); ++road_index) {
//     auto road_segment = routing->mutable_road(road_index);
//     ADEBUG << "road_segment->passage_size()==" <<
//     road_segment->passage_size(); for (int passage_index = 0; passage_index <
//     road_segment->passage_size();
//          ++passage_index) {
//       auto passage = road_segment->passage(passage_index);
//       ADEBUG << "passage.segment_size()==" << passage.segment_size();
//       for (int lane_index = 0; lane_index < passage.segment_size();
//            ++lane_index) {
//         all_routing_lanes_id.emplace(passage.segment(lane_index).id());
//         if (0 == road_index && 0 == passage_index && 0 == lane_index) {
//           start_lane_id = passage.segment(0).id();
//           ADEBUG << "start_lane_id==" << start_lane_id;
//         }
//         if (road_index == routing->road_size() - 1 &&
//             passage_index == road_segment->passage_size() - 1 &&
//             lane_index == passage.segment_size() - 1) {
//           end_lane_id = passage.segment(lane_index).id();
//           ADEBUG << "end_lane_id==" << end_lane_id;
//         }
//       }
//     }
//   }
//   auto end_lane = hdmap_->GetLaneById(hdmap::MakeMapId(end_lane_id));
//   if (end_lane == nullptr) {
//     AERROR << "end_lane == nullptr";
//     return false;
//   }
//   auto end_lane_right_neighbor_ids =
//       end_lane->lane().right_neighbor_forward_lane_id();
//   if (end_lane_right_neighbor_ids.empty()) {
//     ADEBUG << "end_lane_right_neighbor_ids is empty.";
//   } else {
//     const auto &end_lane_right_neighbor_id =
//         end_lane_right_neighbor_ids.at(0).id();
//     end_left_and_right_lane.second = end_lane_right_neighbor_id;
//     ADEBUG << "end_lane_right_neighbor_id=" << end_lane_right_neighbor_id;
//   }

//   auto end_lane_left_neighbor_ids =
//       end_lane->lane().left_neighbor_forward_lane_id();
//   if (end_lane_left_neighbor_ids.empty()) {
//     ADEBUG << "end_lane_left_neighbor_ids is empty.";
//   } else {
//     const auto &end_lane_left_neighbor_id =
//         end_lane_left_neighbor_ids.at(0).id();
//     end_left_and_right_lane.first = end_lane_left_neighbor_id;
//     ADEBUG << "end_lane_left_neighbor_id=" << end_lane_left_neighbor_id;
//   }

//   // left
//   for (int road_index = 0; road_index < routing->road_size(); ++road_index) {
//     auto road_segment = routing->mutable_road(road_index);
//     int passage_size = road_segment->passage_size();
//     for (int passage_index = 0; passage_index < passage_size;
//     ++passage_index) {
//       auto passage = road_segment->passage(passage_index);
//       const auto &current_lane =
//           hdmap_->GetLaneById(hdmap::MakeMapId(passage.segment(0).id()));
//       auto current_lane_type = current_lane->lane().type();
//       bool current_lane_is_virtual =
//           current_lane->lane().left_boundary().virtual_();
//       auto current_lane_left_boundary_type =
//           current_lane->lane().left_boundary().boundary_type(0).types(0);
//       if (hdmap::Lane::PLAY_STREET == current_lane_type ||
//           current_lane_is_virtual ||
//           LaneBoundaryType::DOTTED_WHITE != current_lane_left_boundary_type)
//           {
//         break;
//       }
//       const auto &left_ids =
//           current_lane->lane().left_neighbor_forward_lane_id();

//       if (left_ids.empty()) {
//         ADEBUG << "left_ids is empty.";
//         break;
//       }

//       const auto &left_lane_id = left_ids.at(0).id();
//       bool in_routing_lanes = false;
//       for (auto iter = all_routing_lanes_id.begin();
//            iter != all_routing_lanes_id.end(); ++iter) {
//         if (iter->data() == left_lane_id) {
//           in_routing_lanes = true;
//           break;
//         }
//       }
//       if (in_routing_lanes) {
//         continue;
//       }
//       bool neight_end_lane = false;
//       if (left_lane_id == end_left_and_right_lane.first) {
//         neight_end_lane = true;
//       }

//       apollo::routing::Passage left_passage;
//       auto left_lane = hdmap_->GetLaneById(hdmap::MakeMapId(left_lane_id));
//       auto left_lane_successor = left_lane->lane().successor_id();
//       if (left_lane->lane().successor_id().empty()) {
//         left_passage.set_can_exit(false);
//         left_passage.set_change_lane_type(routing::RIGHT);
//       } else {
//         bool neight_routing = false;
//         for (auto iter = left_lane_successor.begin();
//              iter != left_lane_successor.end(); ++iter) {
//           auto left_lane_successor_right_neighbor =
//               hdmap_->GetLaneById(hdmap::MakeMapId(iter->id()));
//           auto left_lane_successor_right_neighbor_ids =
//               left_lane_successor_right_neighbor->lane()
//                   .right_neighbor_forward_lane_id();
//           if (left_lane_successor_right_neighbor_ids.empty()) {
//             ADEBUG << "left_lane_successor_right_neighbor_ids is empty.";
//             continue;
//           }
//           const auto &left_lane_successor_right_neighbor_id =
//               left_lane_successor_right_neighbor_ids.at(0).id();

//           for (auto iter = all_routing_lanes_id.begin();
//                iter != all_routing_lanes_id.end(); ++iter) {
//             if (iter->data() == left_lane_successor_right_neighbor_id) {
//               ADEBUG << "left_lane_successor_right_neighbor_id="
//                      << left_lane_successor_right_neighbor_id;

//               auto left_lane_successor_right_neighbor = hdmap_->GetLaneById(
//                   hdmap::MakeMapId(left_lane_successor_right_neighbor_id));
//               bool next_lane_is_virtual =
//                   left_lane_successor_right_neighbor->lane()
//                       .left_boundary()
//                       .virtual_();
//               auto next_lane_left_boundary_type =
//                   left_lane_successor_right_neighbor->lane()
//                       .left_boundary()
//                       .boundary_type(0)
//                       .types(0);
//               if (!next_lane_is_virtual && LaneBoundaryType::DOTTED_WHITE ==
//                                                next_lane_left_boundary_type)
//                                                {
//                 neight_routing = true;
//                 break;
//               }
//             }
//           }
//           if (neight_routing) {
//             break;
//           }
//         }
//         if (neight_routing && !neight_end_lane) {
//           left_passage.set_can_exit(true);
//           left_passage.set_change_lane_type(routing::FORWARD);
//         } else {
//           left_passage.set_can_exit(false);
//           left_passage.set_change_lane_type(routing::RIGHT);
//         }
//       }

//       apollo::routing::LaneSegment left_lane_segment;
//       left_lane_segment.set_id(left_lane_id);
//       if (0 ==
//           std::strcmp(start_lane_id.c_str(),
//           passage.segment(0).id().c_str())) {
//         const auto &start_pose =
//             routing->routing_request().waypoint().begin()->pose();
//         double s = 0.0;
//         double l = 0.0;
//         if (!left_lane->GetProjection({start_pose.x(), start_pose.y()}, &s,
//                                       &l)) {
//           left_lane_segment.set_start_s(
//               left_lane->total_length() -
//               (passage.segment(0).end_s() - passage.segment(0).start_s()));
//         } else {
//           left_lane_segment.set_start_s(s);
//         }
//         left_lane_segment.set_end_s(left_lane->total_length());
//       } else if (0 == std::strcmp(end_lane_id.c_str(),
//                                   passage.segment(0).id().c_str())) {
//         left_lane_segment.set_start_s(0);
//         const auto &end_pose =
//             routing->routing_request().waypoint().rbegin()->pose();
//         double s = 0.0;
//         double l = 0.0;
//         if (!left_lane->GetProjection({end_pose.x(), end_pose.y()}, &s, &l))
//         {
//           left_lane_segment.set_end_s(passage.segment(0).end_s());
//         } else {
//           left_lane_segment.set_end_s(s);
//         }
//       } else {
//         left_lane_segment.set_start_s(0);
//         left_lane_segment.set_end_s(left_lane->total_length());
//       }

//       left_passage.add_segment()->CopyFrom(left_lane_segment);
//       road_segment->add_passage()->CopyFrom(left_passage);
//     }
//   }

//   // Update original routing direction
//   UpdateOriginalRoutingPassageDirection(routing);

//   // Check route change lane length
//   CheckRouteChangeLaneLength(routing);

//   for (int i = 0; i < routing->road_size(); ++i) {
//     for (int j = 0; j < routing->road(i).passage_size(); ++j) {
//       for (int n = 0; n < routing->road(i).passage(j).segment_size(); ++n) {
//         AINFO << "road[" << i << "]<" << routing->road(i).id() << ">passage["
//               << j << "]<" << routing->road(i).passage(j).can_exit() << "><"
//               << routing::ChangeLaneType_Name(
//                      routing->road(i).passage(j).change_lane_type())
//               << ">lane[" << n << "]<"
//               << routing->road(i).passage(j).segment(n).id() << "><"
//               << routing->road(i).passage(j).segment(n).start_s() << "><"
//               << routing->road(i).passage(j).segment(n).end_s() << ">";
//       }
//     }
//   }
//   return true;
// }

void RoutingComponent::UpdateOriginalRoutingPassageDirection(
    routing::RoutingResponse *routing) {
  if (nullptr == routing) {
    AERROR << "routing == nullptr";
    return;
  }

  if (kIsEmptyIntValue == routing->road_size()) {
    AERROR << "routing->road_size() == 0";
    return;
  }

  for (int road_index = 0; road_index < routing->road_size() - 1;
       ++road_index) {
    for (int passage_index = 0;
         passage_index < routing->road(road_index).passage_size();
         ++passage_index) {
      auto *current_passage =
          routing->mutable_road(road_index)->mutable_passage(passage_index);
      const auto &current_lane_id = current_passage->segment().rbegin()->id();
      const auto &current_lane =
          hdmap_->GetLaneById(hdmap::MakeMapId(current_lane_id));
      if (nullptr == current_lane) {
        continue;
      }
      auto current_lane_successor = current_lane->lane().successor_id();
      for (auto iter = current_lane_successor.begin();
           iter != current_lane_successor.end(); ++iter) {
        for (int next_road_passage_index = 0;
             next_road_passage_index <
             routing->road(road_index + 1).passage_size();
             ++next_road_passage_index) {
          if (iter->id() == routing->road(road_index + 1)
                                .passage(next_road_passage_index)
                                .segment()
                                .begin()
                                ->id() &&
              !current_passage->can_exit()) {
            // Due to the fact that there may be multiple transition points in
            // the actual situation, it is necessary to search for the entire
            // routing, so there is no need to break.
            current_passage->set_can_exit(true);
            current_passage->set_change_lane_type(routing::FORWARD);
            AINFO << "Current passage(lane id[" << current_lane_id
                  << "]) is not forward direction, need to update forward "
                     "direction.";
          }
        }
      }
    }
  }
}

void RoutingComponent::CheckRouteChangeLaneLength(
    routing::RoutingResponse *routing) {
  if (nullptr == routing) {
    AERROR << "routing == nullptr";
    return;
  }

  if (kIsEmptyIntValue == routing->road_size()) {
    AERROR << "routing->road_size() == 0";
    return;
  }

  for (int road_index = 0; road_index < routing->road_size() - 1;
       ++road_index) {
    for (int passage_index = 0;
         passage_index < routing->road(road_index).passage_size();
         ++passage_index) {
      const auto &passage = routing->road(road_index).passage(passage_index);
      double lane_change_length = CalculatePassageLength(passage);
      if (routing::RIGHT == passage.change_lane_type() && road_index > 0 &&
          lane_change_length <
              FLAGS_min_length_for_extend_passage_lane_change) {
        int update_road_index = road_index - 1;
        std::string first_passage_lane_id = passage.segment().begin()->id();
        while (lane_change_length <
                   FLAGS_min_length_for_extend_passage_lane_change &&
               update_road_index >= 0) {
          bool find_predecessor_passage = false;
          for (int p_index = 0;
               p_index < routing->road(update_road_index).passage_size();
               ++p_index) {
            auto *m_passage = routing->mutable_road(update_road_index)
                                  ->mutable_passage(p_index);
            if (IsPredecessorPassage(*m_passage, first_passage_lane_id)) {
              find_predecessor_passage = true;
              m_passage->set_can_exit(false);
              m_passage->set_change_lane_type(routing::RIGHT);
              lane_change_length += CalculatePassageLength(*m_passage);
              update_road_index -= 1;
              first_passage_lane_id = m_passage->segment().begin()->id();
              break;
            }
          }
          if (!find_predecessor_passage) {
            break;
          }
        }
      }
    }
  }
}

double RoutingComponent::CalculatePassageLength(
    const routing::Passage &passage) {
  double total_length = 0.0;
  for (int i = 0; i < passage.segment_size(); ++i) {
    total_length += passage.segment(i).end_s() - passage.segment(i).start_s();
  }
  return total_length;
}

bool RoutingComponent::IsPredecessorPassage(
    const routing::Passage &passage, const std::string &successor_lane_id) {
  const auto &successor_lane =
      hdmap_->GetLaneById(hdmap::MakeMapId(successor_lane_id));
  if (nullptr == successor_lane) {
    return false;
  }
  const auto &predecessor_lane = successor_lane->lane().predecessor_id();
  for (auto iter = predecessor_lane.begin(); iter != predecessor_lane.end();
       ++iter) {
    if (iter->id() == passage.segment().rbegin()->id()) {
      return true;
    }
  }
  return false;
}

}  // namespace routing
}  // namespace apollo
