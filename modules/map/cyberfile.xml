<package format="2">
  <name>map</name>
  <version>local</version>
  <description>
    Apollo map module.
  </description>

  <maintainer email="<EMAIL>">Apollo</maintainer>
  <license>Apache License 2.0</license>
  <type>module</type>
  <url type="website">https://www.apollo.auto/</url>
  <url type="repository">https://github.com/ApolloAuto/apollo</url>
  <url type="bugtracker">https://github.com/ApolloAuto/apollo/issues</url>

  <depend type="binary" repo_name="common" lib_names="common">common</depend>
  <depend type="binary" repo_name="cyber">cyber</depend>
  <!-- <depend type="binary" repo_name="map-data">map-data</depend> -->
  <depend type="binary" repo_name="common-msgs" lib_names="common-msgs">common-msgs</depend>
  <depend repo_name="com_google_protobuf" lib_names="protobuf">3rd-protobuf</depend>
  <depend repo_name="proj">3rd-proj</depend>
  <depend repo_name="tinyxml2" so_names="tinyxml2">libtinyxml2-dev</depend>
  <depend repo_name="com_github_jbeder_yaml_cpp" lib_names="yaml-cpp">3rd-yaml-cpp</depend>
  <depend repo_name="eigen">3rd-eigen3</depend>
  <depend repo_name="com_google_absl" lib_names="absl">3rd-absl</depend>
  <depend repo_name="com_github_google_glog" lib_names="glog">3rd-glog</depend>
  <depend repo_name="com_google_googletest" lib_names="gtest,gtest_main">3rd-gtest</depend>
  <depend repo_name="com_github_gflags_gflags" lib_names="gflags">3rd-gflags</depend>
  <depend repo_name="com_github_nlohmann_json" lib_names="single_json,json">3rd-nlohmann-json</depend>
  <depend repo_name="boost">3rd-boost</depend>
  <!-- <depend expose="False">3rd-gpus</depend> -->
  <depend expose="False">3rd-rules-python</depend>
  <depend expose="False">3rd-grpc</depend>
  <depend expose="False">3rd-bazel-skylib</depend>
  <depend expose="False">3rd-rules-proto</depend>

  <src_path url="https://github.com/ApolloAuto/apollo">//modules/map</src_path>

</package>
