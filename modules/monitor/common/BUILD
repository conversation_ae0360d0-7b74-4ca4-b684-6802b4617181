load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")

package(default_visibility = ["//visibility:public"])

MONITOR_COPTS = ['-DMODULE_NAME=\\"monitor\\"']

apollo_cc_library(
    name = "recurrent_runner",
    srcs = ["recurrent_runner.cc"],
    hdrs = ["recurrent_runner.h"],
    copts = MONITOR_COPTS,
    deps = [
        "//cyber",
    ],
)

apollo_cc_library(
    name = "monitor_gflags",
    srcs = ["monitor_gflags.cc"],
    hdrs = ["monitor_gflags.h"],
    copts = MONITOR_COPTS,
    deps = [
        "@com_github_gflags_gflags//:gflags",
    ],
)

apollo_cc_test(
    name = "recurrent_runner_test",
    size = "small",
    srcs = ["recurrent_runner_test.cc"],
    copts = MONITOR_COPTS,
    deps = [
        ":recurrent_runner",
        "@com_google_googletest//:gtest_main",
    ],
)

apollo_cc_library(
    name = "monitor_manager",
    srcs = ["monitor_manager.cc"],
    hdrs = ["monitor_manager.h"],
    copts = MONITOR_COPTS,
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common_msgs/chassis_msgs:chassis_proto",
        "//modules/common/adapters:adapter_gflags",
        "//modules/common/monitor_log",
        "//modules/common_msgs/basic_msgs:drive_event_proto",
        "//modules/common/util:util_lib",
        "//modules/common/util:util_tool",
        "//modules/dreamview/backend/common:dreamview_gflags",
        "//modules/dreamview/backend/hmi:hmi_worker",
        "//modules/dreamview/backend/util:hmi_util",
        "//modules/common_msgs/dreamview_msgs:hmi_config_proto",
        "//modules/common_msgs/dreamview_msgs:hmi_mode_proto",
        "//modules/common_msgs/dreamview_msgs:hmi_status_proto",
        "//modules/common_msgs/localization_msgs:pose_proto",
        "//modules/common_msgs/monitor_msgs:system_status_proto",
        "@com_github_gflags_gflags//:gflags",
    ],
)

apollo_package()
cpplint()
