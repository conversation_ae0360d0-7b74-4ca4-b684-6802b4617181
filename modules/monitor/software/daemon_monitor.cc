/******************************************************************************
 * Copyright 2022 The apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "modules/monitor/software/daemon_monitor.h"

#include <limits.h>
#include <sys/statfs.h>
#include <unistd.h>

#include <iostream>
#include <map>
#include <memory>
#include <regex>
#include <string>
#include <utility>

#include "boost/filesystem.hpp"
#include "gflags/gflags.h"
#include "third_party/tinyxml/tinystr.h"
#include "third_party/tinyxml/tinyxml.h"

#include "cyber/common/file.h"
#include "cyber/common/log.h"
#include "cyber/timedelay/timedelay.h"
#include "modules/common/adapters/adapter_gflags.h"
#include "modules/common/util/map_util.h"
#include "modules/monitor/common/monitor_gflags.h"
#include "modules/monitor/common/monitor_manager.h"
#include "modules/monitor/software/summary_monitor.h"

DEFINE_string(daemon_monitor_name, "DaemonMonitor",
              "Name of the daemon monitor.");

DEFINE_double(daemon_monitor_interval, 0.25,
              "Daemon status checking interval in seconds.");

#define IF_DOMAIN_MATCH(channel_name)                           \
  if ((DOMAIN_TYPE == monitor_configs_[channel_name].domain) || \
      (true == monitor_configs_[channel_name].cross_domain))

#define ERR_CODE_MSG(err_code, code) [err_code] = {code, (#err_code) + 2}
namespace apollo {
namespace monitor {
namespace {
const double kMininumTime = 1.0e-6;
const uint16_t byte_2_kb = 10;
const uint16_t byte_2_gb = 30;
const uint16_t kMaxStringLenth = 256;
const uint16_t kDiffTimeThreshold = 2;
const double kDoubleEpsilon = 1.0e-8;
const double kDoubleMaximumDiffTime = 1.0e2;
const double kDoubleDiskCheckFreq = 60.0;
const double kSecond2Nanosecond = 1000000000.0;
const double kMiniFrequencyEpsilon = 1.0e-6;
const double kMaxFrequencyEpsilon = 65535;
const uint16_t kDiskCheckInterval = 60;
const uint16_t kCubtekRadarNormalStatus = 1;
const uint16_t kUdasUltrasonicNormalStatus = 1;
const uint16_t kCacheFrameCount = 2;
const uint8_t kCheckSkipTimes = 10;
const double kTransmissionLatencyDuration = 5.0;
const double kDoubleLocRsDiffTime = 1.0;
const double kDoubleLocKillDiffTime = 5.0;
const double kDoublePerceptionDiffCurTime = 1.0;
const double kDoubleModuleDiffCurTime = 1.0;
const double kDoublePerceptionDiffPredictionTime = 1.0;
const double kDoubleLastKillTimeDiffNowTime = 1.0;
const double k2Sencons = 2.0;
const double kRsSdkHangingCheckPeriod = 11.0;

constexpr const uint32_t kDoubleCheckNetworkFreq = 60 * 1000;

constexpr const char* kLogPath = "/apollo/data/log/";
constexpr const char* kBagPath = "/apollo/data/bag/";
constexpr const char* kWirteTestFilePath = "/apollo/data/log/test.txt";

#if defined __aarch64__
const uint16_t CPU_COUNT = 8;
const int DOMAIN_TYPE = common::DomainType::AARCH64;
constexpr const char* kServiceName = "/apollo/monitor/monitor_service_orin";
#else
const uint16_t CPU_COUNT = 16;
const int DOMAIN_TYPE = common::DomainType::X86;
constexpr const char* kServiceName = "/apollo/monitor/monitor_service_x86";
#endif

static const char* monitored_error_code[][2] = {
    ERR_CODE_MSG(E_OK, "AD000"),
    ERR_CODE_MSG(E_NOT_INIT, "AD802"),
    ERR_CODE_MSG(E_OUT_OF_MEM, "AD704"),
    ERR_CODE_MSG(E_OUT_OF_CPU, "AD706"),
    ERR_CODE_MSG(E_LOW_DISK, "AD703"),
    ERR_CODE_MSG(E_OUT_OF_DISK, "AD70301"),
    ERR_CODE_MSG(E_DIFF_OF_MAP, "AD70302"),
    ERR_CODE_MSG(E_LOW_FREQ_LOCALIZATION, "AD401"),
    ERR_CODE_MSG(E_NO_FREQ_LOCALIZATION, "AD40101"),
    ERR_CODE_MSG(E_LOCALIZATION_STATUS_ABNORMAL, "AD40102"),
    ERR_CODE_MSG(E_LOCALIZATION_DELAY, "AD40103"),
    ERR_CODE_MSG(E_LOC_INIT_IMU_WAITING, "AD40104"),
    ERR_CODE_MSG(E_LOC_INIT_GPS_WAITING, "AD40105"),
    ERR_CODE_MSG(E_LOC_INIT_ODOMETRY_WAITING, "AD40106"),
    ERR_CODE_MSG(E_LOC_INIT_LIDAR_WAITING, "AD40107"),
    ERR_CODE_MSG(E_LOC_INIT_KEY_NOT_FOUND, "AD40108"),
    ERR_CODE_MSG(E_LOW_FREQ_PERCEPTION, "AD101"),
    ERR_CODE_MSG(E_NO_FREQ_PERCEPTION, "AD10101"),
    ERR_CODE_MSG(E_LOW_FREQ_PLANNING, "AD201"),
    ERR_CODE_MSG(E_NO_FREQ_PLANNING, "AD20101"),
    ERR_CODE_MSG(E_LOW_FREQ_CONTROL, "AD301"),
    ERR_CODE_MSG(E_NO_FREQ_CONTROL, "AD30101"),
    ERR_CODE_MSG(E_CONTROL_CALIBRATION_ANOMALY, "AD30102"),
    ERR_CODE_MSG(E_LOW_FREQ_PREDICTION, "AD501"),
    ERR_CODE_MSG(E_NO_FREQ_PREDICTION, "AD50101"),
    ERR_CODE_MSG(E_LOW_FREQ_TRAFFICLIGHT, "AD102"),
    ERR_CODE_MSG(E_NO_FREQ_TRAFFICLIGHT, "AD10201"),
    ERR_CODE_MSG(E_LOW_FREQ_CHASSIS, "AD604"),
    ERR_CODE_MSG(E_NO_FREQ_CHASSIS, "AD60401"),
    ERR_CODE_MSG(E_LOW_FREQ_MEMS, "AD103"),
    ERR_CODE_MSG(E_NO_FREQ_MEMS, "AD10301"),
    ERR_CODE_MSG(E_LOW_FREQ_RADAR, "AD104"),
    ERR_CODE_MSG(E_NO_FREQ_RADAR, "AD10401"),
    ERR_CODE_MSG(E_LOW_FREQ_RS, "AD105"),
    ERR_CODE_MSG(E_NO_FREQ_RS, "AD10501"),
    ERR_CODE_MSG(E_RS_SDK_STATUS_ZERO, "AD10502"),
    ERR_CODE_MSG(E_LOW_FREQ_ULTRASONIC, "AD106"),
    ERR_CODE_MSG(E_NO_FREQ_ULTRASONIC, "AD10601"),
    ERR_CODE_MSG(E_LOW_FREQ_CAMERA, "AD107"),
    ERR_CODE_MSG(E_NO_FREQ_CAMERA, "AD10701"),
    ERR_CODE_MSG(E_LOW_FREQ_RADAR_CUBTK_EOL, "AD108"),
    ERR_CODE_MSG(E_NO_FREQ_RADAR_CUBTK_EOL, "AD10801"),
    ERR_CODE_MSG(E_RADAR_CUBTK_EOL_LEFT_STATUS, "AD10802"),
    ERR_CODE_MSG(E_RADAR_CUBTK_EOL_RIGHT_STATUS, "AD10803"),
    ERR_CODE_MSG(E_RADAR_CUBTK_EOL_REAR_STATUS, "AD10804"),
    ERR_CODE_MSG(E_LOW_FREQ_MCLOUD, "AD109"),
    ERR_CODE_MSG(E_NO_FREQ_MCLOUD, "AD10901"),
    ERR_CODE_MSG(E_MCLOUD_STATUS_ERROR, "AD10902"),
    ERR_CODE_MSG(E_MCLOUD_BOXSTATUS_OPEN, "AD10903"),
    ERR_CODE_MSG(E_NO_FREQ_MONITOR_X86, "AD80301"),    // used in mcloud
    ERR_CODE_MSG(E_NO_FREQ_MONITOR_AARCH, "AD80302"),  // used in mcloud
    ERR_CODE_MSG(E_LOW_FREQ_FRONT_12MM_STATUS, "AD110"),
    ERR_CODE_MSG(E_NO_FREQ_FRONT_12MM_STATUS, "AD11001"),
    ERR_CODE_MSG(E_LOW_FREQ_FRONT_3MM_STATUS, "AD111"),
    ERR_CODE_MSG(E_NO_FREQ_FRONT_3MM_STATUS, "AD11101"),
    ERR_CODE_MSG(E_LOW_FREQ_BACK_3MM_STATUS, "AD112"),
    ERR_CODE_MSG(E_NO_FREQ_BACK_3MM_STATUS, "AD11201"),
    ERR_CODE_MSG(E_DISK_WRITE_STATUS, "AD705"),
    ERR_CODE_MSG(E_LOG_WRITE_STATUS, "AD70501"),
    ERR_CODE_MSG(E_CYBER_RECORDER_WRITE_STATUS, "AD70502"),
    ERR_CODE_MSG(E_LOW_FREQ_MEMS_STATUS, "AD113"),
    ERR_CODE_MSG(E_NO_FREQ_MEMS_STATUS, "AD11301"),
    ERR_CODE_MSG(E_MEMS_FRONT_LEFT_RIGHT_STATUS_ABNORMAL, "AD11302"),
    ERR_CODE_MSG(E_MEMS_LEFT_RIGHT_STATUS_ABNORMAL, "AD11303"),
    ERR_CODE_MSG(E_MEMS_FRONT_LEFT_STATUS_ABNORMAL, "AD11304"),
    ERR_CODE_MSG(E_MEMS_LEFT_STATUS_ABNORMAL, "AD11305"),
    ERR_CODE_MSG(E_MEMS_FRONT_RIGHT_STATUS_ABNORMAL, "AD11306"),
    ERR_CODE_MSG(E_MEMS_RIGHT_STATUS_ABNORMAL, "AD11307"),
    ERR_CODE_MSG(E_MEMS_FRONT_STATUS_ABNORMAL, "AD11308"),
    ERR_CODE_MSG(E_LOW_FREQ_UDAS_ULTRASONIC_EOL, "AD114"),
    ERR_CODE_MSG(E_NO_FREQ_UDAS_ULTRASONIC_EOL, "AD11401"),
    ERR_CODE_MSG(E_UDAS_ULTRASONIC_RIGHT_STATUS, "AD11402"),
    ERR_CODE_MSG(E_UDAS_ULTRASONIC_MIDDLE_RIGHT_STATUS, "AD11403"),
    ERR_CODE_MSG(E_UDAS_ULTRASONIC_MIDDLE_LEFT_STATUS, "AD11404"),
    ERR_CODE_MSG(E_UDAS_ULTRASONIC_LEFT_STATUS, "AD11405"),
    ERR_CODE_MSG(E_LOW_FREQ_IMU_RAW, "AD115"),
    ERR_CODE_MSG(E_NO_FREQ_IMU_RAW, "AD11501"),
    ERR_CODE_MSG(E_EXCEEDING_TIME_DIFF, "AD708"),
    ERR_CODE_MSG(E_NETNOTREACH, "AD709"),
    ERR_CODE_MSG(E_NETDOWN, "AD70901"),
};

inline void SetErrorInFoCodeMsg(monitor::error_info* error_info,
                                const errorcode error_code) {
  error_info->error = monitored_error_code[error_code][0];
  error_info->msg = monitored_error_code[error_code][1];
}
inline void SetFaultDataCodeMsg(monitor::FaultData* fault_info,
                                const errorcode error_code) {
  fault_info->set_code(monitored_error_code[error_code][0]);
  fault_info->set_code_msg(monitored_error_code[error_code][1]);
}

std::unique_ptr<std::mutex> mutex_error_code = std::make_unique<std::mutex>();
}  // namespace

bool DaemonMonitor::LoadMonitorConfig() {
  TiXmlDocument doc;
#ifdef __aarch64__
  doc.LoadFile("/apollo/modules/monitor/config/monitor_config_aarch64.xml");
#else
  doc.LoadFile("/apollo/modules/monitor/config/monitor_config_x86.xml");
#endif
  if (doc.Error()) {
    ADEBUG << "monitor_config_{plaform}.xml open error";
    return false;
  }
  const TiXmlElement* xml_root = doc.FirstChildElement("config");
  const TiXmlElement* xml_config = xml_root->FirstChildElement("process");
  const TiXmlElement* xml_element = nullptr;

  if (xml_config) {
    for (; xml_config != NULL;
         xml_config = xml_config->NextSiblingElement("process")) {
      MonitorConfig config;
      config.process_name = xml_config->Attribute("name");
      xml_element = xml_config->FirstChildElement("channel");
      if (xml_element) {
        config.channel = xml_element->GetText();
      }
      xml_element = xml_config->FirstChildElement("freq_threshold");
      if (xml_element) {
        config.threshold_freq = std::stof(xml_element->GetText());
      }
      xml_element = xml_config->FirstChildElement("freq_expected");
      if (xml_element) {
        config.expected_freq = std::stof(xml_element->GetText());
      }
      xml_element = xml_config->FirstChildElement("domain_type");
      if (xml_element) {
        config.domain = !strcmp(xml_element->GetText(), "aarch64")
                            ? common::DomainType::AARCH64
                            : common::DomainType::X86;
      }
      xml_element = xml_config->FirstChildElement("cross_domain");
      if (xml_element) {
        const char* value = xml_element->GetText();
        if (value && std::string(value) == "true") {
          config.cross_domain = true;
        }
      }

      monitor_configs_.emplace(config.channel, config);
    }
  } else {
    AERROR << "xml has no channel";
    return false;
  }
  AINFO << "monitor_config.xml process size:" << monitor_configs_.size();

  xml_config = xml_root->FirstChildElement("running_process");
  xml_config = xml_config->FirstChildElement("process_name");
  if (xml_config) {
    for (; xml_config != NULL;
         xml_config = xml_config->NextSiblingElement("process_name")) {
      if (xml_config) {
        required_process_.emplace_back(xml_config->GetText());
      }
    }
  } else {
    AERROR << "xml has no running process";
    return false;
  }
  return true;
}

void DaemonMonitor::BootSwitchConfigInit() {
  std::string file_path =
      "/apollo/modules/monitor/config/switch_map_config.pb.txt";
  apollo::cyber::common::GetProtoFromFile(file_path, &switch_config_);
#ifdef __x86_64__
  switch_config_map_[1] = &switch_config_.x86().map_config();
  switch_config_map_[2] = &switch_config_.x86().models_config();
  switch_config_map_[3] = &switch_config_.x86().files_config();
#else
  switch_config_map_[1] = &switch_config_.orin().map_config();
  switch_config_map_[2] = &switch_config_.orin().models_config();
  switch_config_map_[3] = &switch_config_.orin().files_config();
#endif
}

void DaemonMonitor::InitChannelToWarning() {
  channel_to_warning_.emplace(FLAGS_perception_obstacle_topic,
                              E_LOW_FREQ_PERCEPTION);
#ifdef __aarch64__
  channel_to_warning_.emplace(FLAGS_planning_trajectory_topic + "_tcp",
                              E_LOW_FREQ_PLANNING);
  channel_to_warning_.emplace(FLAGS_control_command_topic + "_tcp",
                              E_LOW_FREQ_CONTROL);
  channel_to_warning_.emplace(FLAGS_mcloud_info_topic + "_tcp",
                              E_LOW_FREQ_MCLOUD);
  channel_to_warning_.emplace(FLAGS_localization_topic, E_LOW_FREQ_LOCALIZATION);
  channel_to_warning_.emplace(FLAGS_prediction_topic, E_LOW_FREQ_PREDICTION);
  channel_to_warning_.emplace(FLAGS_traffic_light_detection_topic, E_LOW_FREQ_TRAFFICLIGHT);
#else
  channel_to_warning_.emplace(FLAGS_planning_trajectory_topic,
                              E_LOW_FREQ_PLANNING);
  channel_to_warning_.emplace(FLAGS_control_command_topic, E_LOW_FREQ_CONTROL);
  channel_to_warning_.emplace(FLAGS_mcloud_info_topic, E_LOW_FREQ_MCLOUD);
  channel_to_warning_.emplace(FLAGS_localization_topic + "_tcp", E_LOW_FREQ_LOCALIZATION);
  channel_to_warning_.emplace(FLAGS_prediction_topic + "_tcp", E_LOW_FREQ_PREDICTION);
  channel_to_warning_.emplace(FLAGS_traffic_light_detection_topic + "_tcp", E_LOW_FREQ_TRAFFICLIGHT);
#endif

  channel_to_warning_.emplace(FLAGS_chassis_topic, E_LOW_FREQ_CHASSIS);
  channel_to_warning_.emplace(FLAGS_tracker_mems_topic, E_LOW_FREQ_MEMS);
  channel_to_warning_.emplace(FLAGS_tracker_radar_topic, E_LOW_FREQ_RADAR);
  channel_to_warning_.emplace(FLAGS_tracker_rs_topic, E_LOW_FREQ_RS);
  channel_to_warning_.emplace(FLAGS_tracker_ultrasonic_topic,
                              E_LOW_FREQ_ULTRASONIC);
  channel_to_warning_.emplace(FLAGS_tracker_camera_topic, E_LOW_FREQ_CAMERA);
  channel_to_warning_.emplace(FLAGS_cubtek_radar_eol_topic,
                              E_LOW_FREQ_RADAR_CUBTK_EOL);
  channel_to_warning_.emplace(FLAGS_front_12mm_status_topic,
                              E_LOW_FREQ_FRONT_12MM_STATUS);
  channel_to_warning_.emplace(FLAGS_front_3mm_status_topic,
                              E_LOW_FREQ_FRONT_3MM_STATUS);
  channel_to_warning_.emplace(FLAGS_back_3mm_status_topic,
                              E_LOW_FREQ_BACK_3MM_STATUS);
  channel_to_warning_.emplace(FLAGS_mems_status_topic, E_LOW_FREQ_MEMS_STATUS);
  channel_to_warning_.emplace(FLAGS_udas_ultrasonic_eol_topic,
                              E_LOW_FREQ_UDAS_ULTRASONIC_EOL);
  channel_to_warning_.emplace(FLAGS_imu_raw_topic, E_LOW_FREQ_IMU_RAW);
}

void DaemonMonitor::InitChannelToError() {
  channel_to_error_.emplace(FLAGS_perception_obstacle_topic,
                            E_NO_FREQ_PERCEPTION);
#ifdef __aarch64__
  channel_to_error_.emplace(FLAGS_planning_trajectory_topic + "_tcp",
                            E_NO_FREQ_PLANNING);
  channel_to_error_.emplace(FLAGS_control_command_topic + "_tcp",
                            E_NO_FREQ_CONTROL);
  channel_to_error_.emplace(FLAGS_mcloud_info_topic + "_tcp", E_NO_FREQ_MCLOUD);
  channel_to_error_.emplace(FLAGS_localization_topic, E_NO_FREQ_LOCALIZATION);
  channel_to_error_.emplace(FLAGS_prediction_topic, E_NO_FREQ_PREDICTION);
  channel_to_error_.emplace(FLAGS_traffic_light_detection_topic, E_NO_FREQ_TRAFFICLIGHT);
#else
  channel_to_error_.emplace(FLAGS_planning_trajectory_topic,
                            E_NO_FREQ_PLANNING);
  channel_to_error_.emplace(FLAGS_control_command_topic, E_NO_FREQ_CONTROL);
  channel_to_error_.emplace(FLAGS_mcloud_info_topic, E_NO_FREQ_MCLOUD);
  channel_to_error_.emplace(FLAGS_localization_topic + "_tcp", E_NO_FREQ_LOCALIZATION);
  channel_to_error_.emplace(FLAGS_prediction_topic + "_tcp", E_NO_FREQ_PREDICTION);
  channel_to_error_.emplace(FLAGS_traffic_light_detection_topic + "_tcp", E_NO_FREQ_TRAFFICLIGHT);
#endif
  channel_to_error_.emplace(FLAGS_chassis_topic, E_NO_FREQ_CHASSIS);
  channel_to_error_.emplace(FLAGS_tracker_mems_topic, E_NO_FREQ_MEMS);
  channel_to_error_.emplace(FLAGS_tracker_radar_topic, E_NO_FREQ_RADAR);
  channel_to_error_.emplace(FLAGS_tracker_rs_topic, E_NO_FREQ_RS);
  channel_to_error_.emplace(FLAGS_tracker_ultrasonic_topic,
                            E_NO_FREQ_ULTRASONIC);
  channel_to_error_.emplace(FLAGS_tracker_camera_topic, E_NO_FREQ_CAMERA);
  channel_to_error_.emplace(FLAGS_cubtek_radar_eol_topic,
                            E_NO_FREQ_RADAR_CUBTK_EOL);
  channel_to_error_.emplace(FLAGS_front_12mm_status_topic,
                            E_NO_FREQ_FRONT_12MM_STATUS);
  channel_to_error_.emplace(FLAGS_front_3mm_status_topic,
                            E_NO_FREQ_FRONT_3MM_STATUS);
  channel_to_error_.emplace(FLAGS_back_3mm_status_topic,
                            E_NO_FREQ_BACK_3MM_STATUS);
  channel_to_error_.emplace(FLAGS_mems_status_topic, E_NO_FREQ_MEMS_STATUS);
  channel_to_error_.emplace(FLAGS_udas_ultrasonic_eol_topic,
                            E_NO_FREQ_UDAS_ULTRASONIC_EOL);
  channel_to_error_.emplace(FLAGS_imu_raw_topic, E_NO_FREQ_IMU_RAW);
}

void DaemonMonitor::InitFrameTime() {
  auto time_now = apollo::cyber::Time::MonoTime();
  frame_time_last_.emplace(FLAGS_perception_obstacle_topic, time_now);
#ifdef __aarch64__
  frame_time_last_.emplace(FLAGS_planning_trajectory_topic + "_tcp", time_now);
  frame_time_last_.emplace(FLAGS_control_command_topic + "_tcp", time_now);
  frame_time_last_.emplace(FLAGS_mcloud_info_topic + "_tcp", time_now);
  frame_time_last_.emplace(FLAGS_localization_topic, time_now);
  frame_time_last_.emplace(FLAGS_prediction_topic, time_now);
  frame_time_last_.emplace(FLAGS_traffic_light_detection_topic, time_now);
#else
  frame_time_last_.emplace(FLAGS_planning_trajectory_topic, time_now);
  frame_time_last_.emplace(FLAGS_control_command_topic, time_now);
  frame_time_last_.emplace(FLAGS_mcloud_info_topic, time_now);
  frame_time_last_.emplace(FLAGS_localization_topic + "_tcp", time_now);
  frame_time_last_.emplace(FLAGS_prediction_topic + "_tcp", time_now);
  frame_time_last_.emplace(FLAGS_traffic_light_detection_topic + "_tcp", time_now);
#endif
  frame_time_last_.emplace(FLAGS_chassis_topic, time_now);
  frame_time_last_.emplace(FLAGS_tracker_mems_topic, time_now);
  frame_time_last_.emplace(FLAGS_tracker_radar_topic, time_now);
  frame_time_last_.emplace(FLAGS_tracker_rs_topic, time_now);
  frame_time_last_.emplace(FLAGS_tracker_ultrasonic_topic, time_now);
  frame_time_last_.emplace(FLAGS_tracker_camera_topic, time_now);
  frame_time_last_.emplace(FLAGS_cubtek_radar_eol_topic, time_now);
  frame_time_last_.emplace(FLAGS_front_12mm_status_topic, time_now);
  frame_time_last_.emplace(FLAGS_front_3mm_status_topic, time_now);
  frame_time_last_.emplace(FLAGS_back_3mm_status_topic, time_now);
  frame_time_last_.emplace(FLAGS_mems_status_topic, time_now);
  frame_time_last_.emplace(FLAGS_udas_ultrasonic_eol_topic, time_now);
  frame_time_last_.emplace(FLAGS_imu_raw_topic, time_now);

  time_now = apollo::cyber::Time::MonoTime();
  frame_time_current_.emplace(FLAGS_perception_obstacle_topic, time_now);
#ifdef __aarch64__
  frame_time_current_.emplace(FLAGS_planning_trajectory_topic + "_tcp",
                              time_now);
  frame_time_current_.emplace(FLAGS_control_command_topic + "_tcp", time_now);
  frame_time_current_.emplace(FLAGS_mcloud_info_topic + "_tcp", time_now);
  frame_time_current_.emplace(FLAGS_localization_topic, time_now);
  frame_time_current_.emplace(FLAGS_prediction_topic, time_now);
  frame_time_current_.emplace(FLAGS_traffic_light_detection_topic, time_now);
#else
  frame_time_current_.emplace(FLAGS_planning_trajectory_topic, time_now);
  frame_time_current_.emplace(FLAGS_control_command_topic, time_now);
  frame_time_current_.emplace(FLAGS_mcloud_info_topic, time_now);
  frame_time_current_.emplace(FLAGS_localization_topic + "_tcp", time_now);
  frame_time_current_.emplace(FLAGS_prediction_topic + "_tcp", time_now);
  frame_time_current_.emplace(FLAGS_traffic_light_detection_topic + "_tcp", time_now);
#endif
  frame_time_current_.emplace(FLAGS_chassis_topic, time_now);
  frame_time_current_.emplace(FLAGS_tracker_mems_topic, time_now);
  frame_time_current_.emplace(FLAGS_tracker_radar_topic, time_now);
  frame_time_current_.emplace(FLAGS_tracker_rs_topic, time_now);
  frame_time_current_.emplace(FLAGS_tracker_ultrasonic_topic, time_now);
  frame_time_current_.emplace(FLAGS_tracker_camera_topic, time_now);
  frame_time_current_.emplace(FLAGS_cubtek_radar_eol_topic, time_now);
  frame_time_current_.emplace(FLAGS_front_12mm_status_topic, time_now);
  frame_time_current_.emplace(FLAGS_front_3mm_status_topic, time_now);
  frame_time_current_.emplace(FLAGS_back_3mm_status_topic, time_now);
  frame_time_current_.emplace(FLAGS_mems_status_topic, time_now);
  frame_time_current_.emplace(FLAGS_udas_ultrasonic_eol_topic, time_now);
  frame_time_current_.emplace(FLAGS_imu_raw_topic, time_now);
}

void DaemonMonitor::CreateChannelToErrorMatrix() {
  InitChannelToWarning();
  InitChannelToError();
  InitFrameTime();
}

DaemonMonitor::DaemonMonitor()
    : RecurrentRunner(FLAGS_daemon_monitor_name,
                      FLAGS_daemon_monitor_interval) {
  bool load_xml_success = LoadMonitorConfig();
  AINFO_IF(!load_xml_success) << "DaemonMonitor load xml failed";
  CreateChannelToErrorMatrix();
  InitTopicMapData();
  std::string node_name = "DaemonMonitorNode";
#ifdef __aarch64__
  node_name += "aarch64";
#else
  node_name += "x86";
#endif
  daemon_monitor_node_ = apollo::cyber::CreateNode(node_name);

  if (DOMAIN_TYPE == common::DomainType::X86) {
    CreateCheckNetworkTask();
  }

  CreateChannelMonitorReader();

  system_monitor_ = std::make_unique<monitor::SystemMonitor>();

  auto channel_name = FLAGS_system_monitor_x86_topic;
  if (DOMAIN_TYPE == common::DomainType::AARCH64) {
    channel_name = FLAGS_system_monitor_aarch_topic;
  }
  AINFO << "monitor channel name:" << channel_name;
  monitor_writer_ =
      daemon_monitor_node_->CreateWriter<apollo::monitor::MonitoredData>(
          channel_name);
  if (DOMAIN_TYPE == common::DomainType::X86) {
    monitor_reader_ =
        daemon_monitor_node_->CreateReader<apollo::monitor::MonitoredData>(
            FLAGS_system_monitor_aarch_topic);
  }
  monited_data_ = std::make_shared<apollo::monitor::MonitoredData>();

  BootSwitchConfigInit();
  boot_switch_service_ =
      daemon_monitor_node_->CreateService<apollo::mcloud::McloudMessage,
                                          apollo::mcloud::McloudMessage>(
          kServiceName,
          [&](const std::shared_ptr<apollo::mcloud::McloudMessage>& request,
              std::shared_ptr<apollo::mcloud::McloudMessage>& response) {
            switch (request->type()) {
              case apollo::mcloud::Type::BOOT_SWITCH: {
                response->mutable_boot_switch()->set_switch_result(0);
                if (request->has_boot_switch() &&
                    request->boot_switch().switch_type()) {
                  BootSwitch(request->boot_switch().switch_type());
                  response->mutable_boot_switch()->set_switch_result(1);
                }
              } break;
              case apollo::mcloud::Type::ADRECOVERY: {
                response->mutable_ad_recovery()->set_ad_recovery_result(0);
                if (request->has_ad_recovery() &&
                    request->ad_recovery().ad_recovery_type()) {
                  AdRecovery(request->ad_recovery().ad_recovery_type());
                  response->mutable_ad_recovery()->set_ad_recovery_result(1);
                }
              } break;
              default:
                break;
            }
          });
}

void DaemonMonitor::InitTopicMapData() {
  auto setup_mutex = [this](const std::string& channel,
                            const bool& monitor_switch,
                            const module_type& module) {
    IF_DOMAIN_MATCH(channel) {
      channel_freqs_.emplace(channel, ChannelFreq<double, int>());
    }
    monitor_configs_[channel].module = module;
    channel_to_switch_flag_.emplace(channel, monitor_switch);
  };
  setup_mutex(FLAGS_chassis_topic, FLAGS_chassis_topic_monitor_switch,
              module_type::E_CHASSIS);
  setup_mutex(FLAGS_perception_obstacle_topic,
              FLAGS_perceptionObstacle_topic_monitor_switch,
              module_type::E_PERCEPTION);
#ifdef __aarch64__
  setup_mutex(FLAGS_planning_trajectory_topic + "_tcp",
              FLAGS_planning_topic_monitor_switch, module_type::E_PLANNING);
  setup_mutex(FLAGS_control_command_topic + "_tcp",
              FLAGS_control_topic_monitor_switch, module_type::E_CONTROL);
  setup_mutex(FLAGS_mcloud_info_topic + "_tcp",
              FLAGS_mcloud_topic_monitor_switch, module_type::E_MCLOUD);
  setup_mutex(FLAGS_traffic_light_detection_topic,
              FLAGS_trafficLigth_topic_monitor_switch,
              module_type::E_TRAFFIC_LIGHT);
  setup_mutex(FLAGS_prediction_topic, FLAGS_prediction_topic_monitor_switch,
              module_type::E_PREDICTION);
  setup_mutex(FLAGS_localization_topic, FLAGS_localization_topic_monitor_switch,
              module_type::E_LOCALIZATION);
#else
  setup_mutex(FLAGS_planning_trajectory_topic,
              FLAGS_planning_topic_monitor_switch, module_type::E_PLANNING);
  setup_mutex(FLAGS_control_command_topic, FLAGS_control_topic_monitor_switch,
              module_type::E_CONTROL);
  setup_mutex(FLAGS_mcloud_info_topic, FLAGS_mcloud_topic_monitor_switch,
              module_type::E_MCLOUD);
  setup_mutex(FLAGS_traffic_light_detection_topic + "_tcp",
              FLAGS_trafficLigth_topic_monitor_switch,
              module_type::E_TRAFFIC_LIGHT);
  setup_mutex(FLAGS_prediction_topic + "_tcp", FLAGS_prediction_topic_monitor_switch,
              module_type::E_PREDICTION);
  setup_mutex(FLAGS_localization_topic + "_tcp", FLAGS_localization_topic_monitor_switch,
              module_type::E_LOCALIZATION);
#endif
  setup_mutex(FLAGS_tracker_mems_topic, FLAGS_mems_topic_monitor_switch,
              module_type::E_MEMS);
  setup_mutex(FLAGS_tracker_radar_topic, FLAGS_radar_topic_monitor_switch,
              module_type::E_RADAR);
  setup_mutex(FLAGS_tracker_rs_topic, FLAGS_rs_topic_monitor_switch,
              module_type::E_RS);
  setup_mutex(FLAGS_tracker_ultrasonic_topic,
              FLAGS_ultrasonic_topic_monitor_switch, module_type::E_ULTRASONIC);
  setup_mutex(FLAGS_tracker_camera_topic, FLAGS_camera_topic_monitor_switch,
              module_type::E_CAMERA);
  setup_mutex(FLAGS_cubtek_radar_eol_topic, FLAGS_cubtek_radar_eol_switch,
              module_type::E_RADAR_CUBTEK);
  setup_mutex(FLAGS_front_12mm_status_topic,
              FLAGS_front_12mm_status_topic_monitor_switch,
              module_type::E_CAMERA);
  setup_mutex(FLAGS_front_3mm_status_topic,
              FLAGS_front_3mm_status_topic_monitor_switch,
              module_type::E_CAMERA);
  setup_mutex(FLAGS_back_3mm_status_topic,
              FLAGS_back_3mm_status_topic_monitor_switch,
              module_type::E_CAMERA);
  setup_mutex(FLAGS_mems_status_topic, FLAGS_mems_status_topic_monitor_switch,
              module_type::E_MEMS);
  setup_mutex(FLAGS_udas_ultrasonic_eol_topic,
              FLAGS_udas_ultrasonic_eol_monitor_switch,
              module_type::E_UDAS_ULTRASONIC);
  setup_mutex(FLAGS_imu_raw_topic, FLAGS_imu_raw_topic_monitor_switch,
              module_type::E_IMU_RAW);
}

void DaemonMonitor::CreateChannelMonitorReader() {
  CreateCommonReader();
  CreatePlanningReader();
  CreateLocalizationReader();
  CreatePerceptionTrackerReader();
  CreateCubtekUdasReader();
  CreateMCloudReader();
  CreateCameraStatusReader();
  CreateMemsStatusReader();
  CreateUdasUltrasonicReader();
  CreateImuRawReader();
  CreateLocalizationDelay();
}

void DaemonMonitor::CreateLocalizationDelay() {
  IF_DOMAIN_MATCH(FLAGS_localization_delay_topic) {
    localization_delay_reader_ =
        daemon_monitor_node_->CreateReader<apollo::localization::RsLocStatus>(
            FLAGS_localization_delay_topic,
            [this](
                const std::shared_ptr<apollo::localization::RsLocStatus>& msg) {
              auto time_stamp = apollo::cyber::Clock::NowInSeconds();
              if (apollo::localization::RsLocalizationStatus::RS_LOC_DELAY ==
                  msg->rs_localization_status()) {
                error_info info;
                info.module = module_type::E_LOCALIZATION;
                info.duration_second = 0;
                info.timestamp = time_stamp;
                info.level = E_WARNING;
                info.domain =
                    monitor_configs_[FLAGS_localization_delay_topic].domain;
                SetErrorInFoCodeMsg(&info, E_LOCALIZATION_DELAY);
                std::lock_guard<std::mutex> lock(*mutex_error_code);
                error_info_recorder_.emplace(info.error, info);
              }
            });
  }
}

void DaemonMonitor::CreateCommonReader() {
  IF_DOMAIN_MATCH(FLAGS_chassis_topic) {
    chassis_reader_ =
        daemon_monitor_node_->CreateReader<apollo::canbus::Chassis>(
            FLAGS_chassis_topic,
            [this](const std::shared_ptr<apollo::canbus::Chassis>& msg) {
              if (DOMAIN_TYPE == common::DomainType::X86) {
                static uint8_t times = 0;
                // No need to check every time.
                if (kCheckSkipTimes == ++times) {
                  JudgeTransimissionLatency(msg->header().timestamp_sec());
                  times = 0;
                }
              }
              MsgCheckFunc(msg, FLAGS_chassis_topic, module_type::E_CHASSIS,
                           E_EMERGENCY_STOP, E_PULL_OVER);
            });
  }

  std::string control_command_topic = FLAGS_control_command_topic;
#ifdef __aarch64__
  control_command_topic += "_tcp";
#endif  

  IF_DOMAIN_MATCH(control_command_topic) {
    auto deal_control_func =
        [control_command_topic,
         this](const std::shared_ptr<apollo::control::ControlCommand>& msg) {
          if (msg->has_vehicle_calibration_anomaly() &&
              msg->vehicle_calibration_anomaly()) {
            error_info info;
            info.module = module_type::E_CONTROL;
            info.duration_second = 0;
            info.timestamp = msg->header().timestamp_sec();
            info.level = E_PULL_OVER;
            info.domain = monitor_configs_[control_command_topic].domain;
            SetErrorInFoCodeMsg(&info, E_CONTROL_CALIBRATION_ANOMALY);
            if (channel_to_switch_flag_[control_command_topic]) {
              std::lock_guard<std::mutex> lock(*mutex_error_code);
              error_info_recorder_.emplace(info.error, info);
            }
          }
        };

    control_reader_ =
        daemon_monitor_node_->CreateReader<apollo::control::ControlCommand>(
            control_command_topic,
            [deal_control_func, control_command_topic,
             this](const std::shared_ptr<apollo::control::ControlCommand>& msg) {
              deal_control_func(msg);

              MsgCheckFunc(msg, control_command_topic,
                           module_type::E_CONTROL, E_EMERGENCY_STOP,
                           E_PULL_OVER);
            });
  }

  IF_DOMAIN_MATCH(FLAGS_perception_obstacle_topic) {
    perception_obstacle_reader_ =
        daemon_monitor_node_
            ->CreateReader<apollo::perception::PerceptionObstacles>(
                FLAGS_perception_obstacle_topic,
                [this](const std::shared_ptr<
                       apollo::perception::PerceptionObstacles>& msg) {
                  TIMEDELAY_TRACE(DELAYTRACE_MONITOR, "monitor_fusion",
                                  msg->header().sequence_num())
                  MsgCheckFunc(msg, FLAGS_perception_obstacle_topic,
                               module_type::E_PERCEPTION, E_EMERGENCY_STOP,
                               E_EMERGENCY_STOP);
                });
  }

std::string traffic_light_detection_topic = FLAGS_traffic_light_detection_topic;
#ifndef __aarch64__
  traffic_light_detection_topic += "_tcp";
#endif  
  IF_DOMAIN_MATCH(traffic_light_detection_topic) {
    traffic_light_reader_ =
        daemon_monitor_node_
            ->CreateReader<apollo::perception::TrafficLightDetection>(
                traffic_light_detection_topic,
                [this, traffic_light_detection_topic](const std::shared_ptr<
                       apollo::perception::TrafficLightDetection>& msg) {
                  MsgCheckFunc(msg, traffic_light_detection_topic,
                               module_type::E_TRAFFIC_LIGHT, E_EMERGENCY_STOP,
                               E_PULL_OVER);
                });
  }

std::string prediction_topic = FLAGS_prediction_topic;
#ifndef __aarch64__
  prediction_topic += "_tcp";
#endif  
  IF_DOMAIN_MATCH(prediction_topic) {
    prediction_reader_ = daemon_monitor_node_->CreateReader<
        apollo::prediction::PredictionObstacles>(
        prediction_topic,
        [this, prediction_topic](const std::shared_ptr<apollo::prediction::PredictionObstacles>&
                   msg) {
          TIMEDELAY_TRACE(DELAYTRACE_MONITOR, "monitor_prediction",
                          msg->header().sequence_num())
          MsgCheckFunc(msg, prediction_topic, module_type::E_PREDICTION,
                       E_EMERGENCY_STOP, E_EMERGENCY_STOP);
        });
  }
}

void DaemonMonitor::CreatePlanningReader() {
  std::string planning_trajectory_topic = FLAGS_planning_trajectory_topic;
#ifdef __aarch64__
  planning_trajectory_topic += "_tcp";
#endif
  IF_DOMAIN_MATCH(planning_trajectory_topic) {
    planning_reader_ =
        daemon_monitor_node_->CreateReader<apollo::planning::ADCTrajectory>(
            planning_trajectory_topic,
            [planning_trajectory_topic, this](
                const std::shared_ptr<apollo::planning::ADCTrajectory>& msg) {
              PlanningCheck(msg);
              MsgCheckFunc(msg, planning_trajectory_topic,
                           module_type::E_PLANNING, E_EMERGENCY_STOP,
                           E_PULL_OVER);
            });
  }
}

void DaemonMonitor::CreateLocalizationReader() {
  std::string localization_topic = FLAGS_localization_topic;
#ifndef __aarch64__
  localization_topic += "_tcp";
#endif  
  auto LocalizationFunc =
      [this, localization_topic](const std::shared_ptr<apollo::localization::LocalizationEstimate>&
                 msg) {
        localization_time_stamp_ = cur_time_second_;

        double time_stamp = msg->header().timestamp_sec();
        localization_time_stamp_ = apollo::cyber::Clock::NowInSeconds();
        int rs_localization_status =
            msg->rs_loc_status().rs_localization_status();
        int rs_zone_id_status = msg->rs_loc_status().rs_zone_id_status();
        if (apollo::localization::RS_LOC_NORMAL != rs_localization_status ||
            apollo::localization::ZONE_ID_NORMAL != rs_zone_id_status) {
          error_info info;
          info.module = module_type::E_LOCALIZATION;
          info.duration_second = 0;
          info.timestamp = time_stamp;
          rs_localization_status == apollo::localization::RS_LOC_LOST
              ? info.level = E_EMERGENCY_STOP
              : info.level = E_INFO;
          info.domain = monitor_configs_[localization_topic].domain;
          SetErrorInFoCodeMsg(&info, E_LOCALIZATION_STATUS_ABNORMAL);
          if (channel_to_switch_flag_[localization_topic]) {
            std::lock_guard<std::mutex> lock(*mutex_error_code);
            error_info_recorder_.emplace(info.error, info);
          }
          AERROR << "channel name:" << localization_topic
                 << " in abnormal status" << rs_localization_status << "|"
                 << rs_zone_id_status;
        }
      };
  IF_DOMAIN_MATCH(localization_topic) {
    localization_reader_ =
        daemon_monitor_node_
            ->CreateReader<apollo::localization::LocalizationEstimate>(
                localization_topic,
                [LocalizationFunc,
                 this, localization_topic](const std::shared_ptr<
                       apollo::localization::LocalizationEstimate>& msg) {
                  if (DOMAIN_TYPE == common::DomainType::X86) {
                    static uint8_t times = 0;
                    // No need to check every time.
                    if (kCheckSkipTimes == ++times) {
                      JudgeTransimissionLatency(msg->header().timestamp_sec());
                      times = 0;
                    }
                  }
                  LocalizationFunc(msg);
                  LocationStatusCheck(msg);
                  MsgCheckFunc(msg, localization_topic,
                               module_type::E_LOCALIZATION, E_EMERGENCY_STOP,
                               E_PULL_OVER);
                });
  }
}

void DaemonMonitor::CreateImuRawReader() {
  IF_DOMAIN_MATCH(FLAGS_imu_raw_topic) {
    imu_raw_reader_ =
        daemon_monitor_node_->CreateReader<apollo::modules::drivers::imu::Imu>(
            FLAGS_imu_raw_topic,
            [this](
                const std::shared_ptr<apollo::modules::drivers::imu::Imu>& msg) {
              MsgCheckFunc(msg, FLAGS_imu_raw_topic, module_type::E_IMU_RAW,
                           E_WARNING, E_WARNING);
            });
  }
}

void DaemonMonitor::CreateUdasUltrasonicReader() {
  IF_DOMAIN_MATCH(FLAGS_udas_ultrasonic_eol_topic) {
    udas_ultrasonic_status_reader_ = daemon_monitor_node_->CreateReader<
        apollo::drivers::UdasUltrasonicEOL>(
        FLAGS_udas_ultrasonic_eol_topic,
        [this](const std::shared_ptr<apollo::drivers::UdasUltrasonicEOL>& msg) {
          double time_stamp = msg->header().timestamp_sec();
          uint16_t udas_ultrasonic_right_status =
              msg->udas_ultrasonic_eol_state(0);
          uint16_t udas_ultrasonic_middle_right_status =
              msg->udas_ultrasonic_eol_state(1);
          uint16_t udas_ultrasonic_middle_left_status =
              msg->udas_ultrasonic_eol_state(2);
          uint16_t udas_ultrasonic_left_status =
              msg->udas_ultrasonic_eol_state(3);
          std::unordered_map<errorcode, uint16_t> udas_ultrasonic_status = {};
          udas_ultrasonic_status.emplace(E_UDAS_ULTRASONIC_RIGHT_STATUS,
                                         udas_ultrasonic_right_status);
          udas_ultrasonic_status.emplace(E_UDAS_ULTRASONIC_MIDDLE_RIGHT_STATUS,
                                         udas_ultrasonic_middle_right_status);
          udas_ultrasonic_status.emplace(E_UDAS_ULTRASONIC_MIDDLE_LEFT_STATUS,
                                         udas_ultrasonic_middle_left_status);
          udas_ultrasonic_status.emplace(E_UDAS_ULTRASONIC_LEFT_STATUS,
                                         udas_ultrasonic_left_status);
          for (const auto& udas_ultrasonic_item : udas_ultrasonic_status) {
            if (kUdasUltrasonicNormalStatus != udas_ultrasonic_item.second) {
              error_info info;
              info.module = module_type::E_UDAS_ULTRASONIC;
              info.duration_second = 0;
              info.timestamp = time_stamp;
              info.level = E_EMERGENCY_STOP;
              info.domain =
                  monitor_configs_[FLAGS_udas_ultrasonic_eol_topic].domain;
              SetErrorInFoCodeMsg(&info, udas_ultrasonic_item.first);
              if (channel_to_switch_flag_[FLAGS_udas_ultrasonic_eol_topic]) {
                std::lock_guard<std::mutex> lock(*mutex_error_code);
                error_info_recorder_.emplace(info.error, info);
              }
              AERROR << "channel name:" << FLAGS_udas_ultrasonic_eol_topic
                     << " ,in abnormal error:"
                     << monitored_error_code[udas_ultrasonic_item.first][0]
                     << " ,in abnormal status:" << udas_ultrasonic_item.second;
            }
          }
          MsgCheckFunc(msg, FLAGS_udas_ultrasonic_eol_topic,
                       module_type::E_UDAS_ULTRASONIC, E_EMERGENCY_STOP,
                       E_EMERGENCY_STOP);
        });
  }
}

void DaemonMonitor::CreateCheckNetworkTask() {
  auto check_network_handler = [this]() {
    auto error_code = CheckNetwork();
    if (E_OK != error_code) {
      error_info info;
      info.module = module_type::E_SYSTEM;
      SetErrorInFoCodeMsg(&info, error_code);
      info.level = E_WARNING;
      info.domain = common::DomainType::X86;
      {
        std::lock_guard<std::mutex> lock(*mutex_error_code);
        error_info_recorder_.emplace(info.error, info);
      }
    }
  };
  time_task_ = std::make_shared<cyber::Timer>(kDoubleCheckNetworkFreq,
                                              check_network_handler, false);
  time_task_->Start();
}

template <
    typename T,
    std::enable_if_t<!std::is_convertible<
        T, std::shared_ptr<apollo::planning::ADCTrajectory>>::value>* = nullptr>
void DaemonMonitor::MsgFreqCheck(const T& msg, const std::string& channel_name,
                                 const module_type& module_type_name,
                                 double* threshold_freq,
                                 double* expected_freq) {
  if (module_type_name == module_type::E_PLANNING) {
    AERROR << "type error, ADCTrajectory is not acceptable";
    *threshold_freq = kMaxFrequencyEpsilon;
    *expected_freq = kMaxFrequencyEpsilon;
  } else {
    *expected_freq = monitor_configs_[channel_name].threshold_freq;
    *threshold_freq = FLAGS_lowfreqthreshold * (*expected_freq);
  }
  return;
}

template <
    typename T,
    std::enable_if_t<std::is_convertible<
        T, std::shared_ptr<apollo::planning::ADCTrajectory>>::value>* = nullptr>
void DaemonMonitor::MsgFreqCheck(const T& msg, const std::string& channel_name,
                                 const module_type& module_type_name,
                                 double* threshold_freq,
                                 double* expected_freq) {
  if (module_type_name == module_type::E_PLANNING) {
    planning::ADCTrajectory::TrajectoryScenario trajectory_scenario =
        msg->trajectory_scenario();
    planning_in_teb_ =
        (trajectory_scenario != apollo::planning::ADCTrajectory::LANEFOLLOW);
    *expected_freq = planning_in_teb_
                         ? FLAGS_teb_planning_required_frq
                         : monitor_configs_[channel_name].threshold_freq;

    *threshold_freq = FLAGS_lowfreqthreshold * (*expected_freq);
  } else {
    AERROR << "type error, can not convert ADCTrajectory";
    *threshold_freq = kMaxFrequencyEpsilon;
    *expected_freq = kMaxFrequencyEpsilon;
  }
  return;
}


void DaemonMonitor::AddedErrorInfo(const std::string& channel_name,
                                   const errorcode error_code,
                                   const error_level level,
                                   const uint64_t timestamp) {
  if (channel_to_switch_flag_[channel_name]) {
    error_info info;
    info.module = monitor_configs_[channel_name].module;
    info.duration_second = 0;
    info.timestamp = timestamp;
    info.level = level;
    info.domain = monitor_configs_[channel_name].domain;
    SetErrorInFoCodeMsg(&info, error_code);

    {
      std::lock_guard<std::mutex> lock(*mutex_error_code);
      error_info_recorder_.emplace(info.error, info);
    }
  }
}

void DaemonMonitor::MsgCheckFunc(
    const auto& msg, const std::string& channel_name,
    const module_type& module_type_name,
    const error_level& log_level_ultra_low_frequency,
    const error_level& log_level_low_frequency) {
  double time_stamp = msg->header().timestamp_sec();
  auto chan_tmp = channel_freqs_[channel_name].load();
  frame_time_current_[channel_name] =
      apollo::cyber::Time(time_stamp);  // apollo::cyber::Time::Now();
  double duration_time =
      (frame_time_current_[channel_name] - frame_time_last_[channel_name])
          .ToNanosecond();
  double threshold_freq, expected_freq;
  MsgFreqCheck(msg, channel_name, module_type_name, &threshold_freq,
               &expected_freq);

  if (duration_time < kSecond2Nanosecond) {
    chan_tmp.SetValue(chan_tmp.GetValue() + 1);
    channel_freqs_[channel_name].store(chan_tmp);
  } else {
    duration_time = duration_time > kMininumTime ? duration_time : kMininumTime;
    double current_freq =
        kSecond2Nanosecond * chan_tmp.GetValue() / duration_time;

    error_info info;
    info.module = module_type_name;
    info.duration_second = 0;
    info.timestamp = time_stamp;
    info.domain = monitor_configs_[channel_name].domain;
    if (current_freq < threshold_freq) {
      info.level = log_level_ultra_low_frequency;
      SetErrorInFoCodeMsg(&info, channel_to_error_[channel_name]);
      if (FLAGS_treat_low_freq_as_error &&
          channel_to_switch_flag_[channel_name]) {
        std::lock_guard<std::mutex> lock(*mutex_error_code);
        error_info_recorder_.emplace(info.error, info);
      }
      AERROR << "channel name:" << channel_name << ", freq: " << current_freq;
    } else if (current_freq < expected_freq && current_freq >= threshold_freq) {
      info.level = log_level_low_frequency;
      SetErrorInFoCodeMsg(&info, channel_to_warning_[channel_name]);
      if (channel_to_switch_flag_[channel_name]) {
        std::lock_guard<std::mutex> lock(*mutex_error_code);
        error_info_recorder_.emplace(info.error, info);
      }
      AINFO << "channel name:" << channel_name << ", freq: " << current_freq;
    }
    {
      chan_tmp.SetKey(floor(time_stamp));
      chan_tmp.SetValue(1);
      channel_freqs_[channel_name].store(chan_tmp);
      frame_time_last_[channel_name] = frame_time_current_[channel_name];
    }
  }
}

void DaemonMonitor::CreateCameraStatusReader() {
  IF_DOMAIN_MATCH(FLAGS_front_12mm_status_topic) {
    front_12mm_camera_status_reader_ =
        daemon_monitor_node_->CreateReader<apollo::drivers::DriverCameraStatus>(
            FLAGS_front_12mm_status_topic,
            [this](const std::shared_ptr<apollo::drivers::DriverCameraStatus>&
                       msg) {
              MsgCheckFunc(msg, FLAGS_front_12mm_status_topic,
                           module_type::E_CAMERA, E_WARNING, E_WARNING);
            });
  }

  IF_DOMAIN_MATCH(FLAGS_front_3mm_status_topic) {
    front_3mm_camera_status_reader_ =
        daemon_monitor_node_->CreateReader<apollo::drivers::DriverCameraStatus>(
            FLAGS_front_3mm_status_topic,
            [this](const std::shared_ptr<apollo::drivers::DriverCameraStatus>&
                       msg) {
              MsgCheckFunc(msg, FLAGS_front_3mm_status_topic,
                           module_type::E_CAMERA, E_WARNING, E_WARNING);
            });
  }

  IF_DOMAIN_MATCH(FLAGS_back_3mm_status_topic) {
    back_3mm_camera_status_reader_ =
        daemon_monitor_node_->CreateReader<apollo::drivers::DriverCameraStatus>(
            FLAGS_back_3mm_status_topic,
            [this](const std::shared_ptr<apollo::drivers::DriverCameraStatus>&
                       msg) {
              MsgCheckFunc(msg, FLAGS_back_3mm_status_topic,
                           module_type::E_CAMERA, E_WARNING, E_WARNING);
            });
  }
}

void DaemonMonitor::BootSwitch(int32_t type) {
  AINFO << "switch type : " << type;
  if (switch_config_map_.find(type) == switch_config_map_.end()) {
    return;
  }

#ifdef __x86_64__
  system("bash /home/<USER>/x86_start.sh");
#else
  system("bash /home/<USER>/arm_start.sh");
#endif

  const auto& configs = *switch_config_map_[type];

  for (const auto& config_command : configs) {
    std::string name = config_command.name();
    KillProcessCmd(name);
  }
}

void DaemonMonitor::AdRecovery(int32_t type) {
  double time_stamp = cyber::Time().Now().ToSecond();
  AINFO << "ad recovery begin time : " << time_stamp;
  system("bash /apollo/scripts/ad_recovery.sh &");
}

void DaemonMonitor::CreateMCloudReader() {
  std::string mcloud_info_topic = FLAGS_mcloud_info_topic;
#ifdef __aarch64__
  mcloud_info_topic += "_tcp";
#endif  
  auto McloudInfoFunc =
      [mcloud_info_topic, this](const std::shared_ptr<apollo::mcloud::McloudInfo>& msg) {
        double time_stamp = msg->header().timestamp_sec();
        bool localization_status = msg->reader_status().localization_status();
        if (!localization_status) {
          error_info info;
          info.module = module_type::E_MCLOUD;
          info.duration_second = 0;
          info.timestamp = time_stamp;
          info.level = E_WARNING;
          info.domain = monitor_configs_[mcloud_info_topic].domain;
          SetErrorInFoCodeMsg(&info, E_MCLOUD_STATUS_ERROR);
          if (channel_to_switch_flag_[mcloud_info_topic]) {
            std::lock_guard<std::mutex> lock(*mutex_error_code);
            error_info_recorder_.emplace(info.error, info);
          }
        }
        // box open when autodriving
        if (msg->has_boxstatus() &&
            mcloud::McloudInfo::BS_SOME_OPENED == msg->boxstatus()) {
          error_info info;
          info.module = module_type::E_MCLOUD;
          info.duration_second = 0;
          info.timestamp = time_stamp;
          info.level = E_EMERGENCY_STOP;
          info.domain = monitor_configs_[mcloud_info_topic].domain;
          SetErrorInFoCodeMsg(&info, E_MCLOUD_BOXSTATUS_OPEN);
          if (channel_to_switch_flag_[mcloud_info_topic]) {
            std::lock_guard<std::mutex> lock(*mutex_error_code);
            error_info_recorder_.emplace(info.error, info);
          }
        }
      };

  IF_DOMAIN_MATCH(mcloud_info_topic) {
    mcloud_reader_ =
        daemon_monitor_node_->CreateReader<apollo::mcloud::McloudInfo>(
            mcloud_info_topic,
            [McloudInfoFunc, mcloud_info_topic,
             this](const std::shared_ptr<apollo::mcloud::McloudInfo>& msg) {
              McloudInfoFunc(msg);
              MsgCheckFunc(msg, mcloud_info_topic, module_type::E_MCLOUD,
                           E_EMERGENCY_STOP, E_PULL_OVER);
            });
  }
}

void DaemonMonitor::CreateMemsStatusReader() {
  IF_DOMAIN_MATCH(FLAGS_mems_status_topic) {
    mems_status_reader_ =
        daemon_monitor_node_->CreateReader<apollo::drivers::zvision::MemsStatus>(
            FLAGS_mems_status_topic,
            [this](const std::shared_ptr<apollo::drivers::zvision::MemsStatus>&
                       msg) {
              double time_stamp = msg->header().timestamp_sec();
              int32_t mems_status = msg->status();
              const int32_t mems_normal_status = 7;
              if (mems_status != mems_normal_status) {
                error_info info;
                info.module = module_type::E_MEMS;
                info.duration_second = 0;
                info.timestamp = time_stamp;
                info.level = E_WARNING;
                info.domain = monitor_configs_[FLAGS_mems_status_topic].domain;
                SetErrorInFoCodeMsg(
                    &info,
                    static_cast<errorcode>(
                        E_MEMS_FRONT_LEFT_RIGHT_STATUS_ABNORMAL + mems_status));
                AERROR << "channel name:" << FLAGS_mems_status_topic
                       << "has abnormal status:"
                       << monitored_error_code
                              [E_MEMS_FRONT_LEFT_RIGHT_STATUS_ABNORMAL +
                               mems_status][0];
                if (channel_to_switch_flag_[FLAGS_mems_status_topic]) {
                  std::lock_guard<std::mutex> lock(*mutex_error_code);
                  error_info_recorder_.emplace(info.error, info);
                }
              }

              MsgCheckFunc(msg, FLAGS_mems_status_topic, module_type::E_MEMS,
                           E_WARNING, E_WARNING);
            });
  }
}

void DaemonMonitor::CreateCubtekUdasReader() {
  IF_DOMAIN_MATCH(FLAGS_cubtek_radar_eol_topic) {
    cubtek_radar_eol_reader_ = daemon_monitor_node_->CreateReader<
        apollo::drivers::RadarEOL>(
        FLAGS_cubtek_radar_eol_topic,
        [this](const std::shared_ptr<apollo::drivers::RadarEOL>& msg) {
          double time_stamp = msg->header().timestamp_sec();

          std::unordered_map<errorcode, uint32_t> cubtek_radar_status = {};
          uint32_t cubtek_radar_left_status = msg->radar_eol_state(0);
          uint32_t cubtek_radar_right_status = msg->radar_eol_state(1);
          uint32_t cubtek_radar_rear_status = msg->radar_eol_state(2);
          cubtek_radar_status.emplace(E_RADAR_CUBTK_EOL_LEFT_STATUS,
                                      cubtek_radar_left_status);
          cubtek_radar_status.emplace(E_RADAR_CUBTK_EOL_RIGHT_STATUS,
                                      cubtek_radar_right_status);
          cubtek_radar_status.emplace(E_RADAR_CUBTK_EOL_REAR_STATUS,
                                      cubtek_radar_rear_status);

          std::for_each(
              cubtek_radar_status.begin(), cubtek_radar_status.end(),
              [&](const std::pair<errorcode, uint32_t>&
                      cubtek_radar_status_item) {
                if (kCubtekRadarNormalStatus !=
                    cubtek_radar_status_item.second) {
                  error_info info;
                  info.module = module_type::E_RADAR_CUBTEK;
                  info.duration_second = 0;
                  info.timestamp = time_stamp;
                  info.level = E_EMERGENCY_STOP;
                  info.domain =
                      monitor_configs_[FLAGS_cubtek_radar_eol_topic].domain;
                  SetErrorInFoCodeMsg(&info, cubtek_radar_status_item.first);
                  if (channel_to_switch_flag_[FLAGS_cubtek_radar_eol_topic]) {
                    std::lock_guard<std::mutex> lock(*mutex_error_code);
                    error_info_recorder_.emplace(info.error, info);
                  }
                  AERROR
                      << "channel name:" << FLAGS_cubtek_radar_eol_topic
                      << " ,in abnormal error:"
                      << monitored_error_code[cubtek_radar_status_item.first][0]
                      << " ,in abnormal status:"
                      << cubtek_radar_status_item.second;
                }
              });

          MsgCheckFunc(msg, FLAGS_cubtek_radar_eol_topic,
                       module_type::E_RADAR_CUBTEK, E_EMERGENCY_STOP,
                       E_PULL_OVER);
        });
  }
}

void DaemonMonitor::CreatePerceptionTrackerReader() {
  IF_DOMAIN_MATCH(FLAGS_tracker_mems_topic) {
    mems_reader_ = daemon_monitor_node_->CreateReader<
        apollo::perception::TransmissionObstacles>(
        FLAGS_tracker_mems_topic,
        [this](const std::shared_ptr<apollo::perception::TransmissionObstacles>&
                   msg) {
          MsgCheckFunc(msg, FLAGS_tracker_mems_topic, module_type::E_MEMS,
                       E_EMERGENCY_STOP, E_PULL_OVER);
        });
  }

  IF_DOMAIN_MATCH(FLAGS_tracker_radar_topic) {
    radar_reader_ = daemon_monitor_node_->CreateReader<
        apollo::perception::TransmissionObstacles>(
        FLAGS_tracker_radar_topic,
        [this](const std::shared_ptr<apollo::perception::TransmissionObstacles>&
                   msg) {
          MsgCheckFunc(msg, FLAGS_tracker_radar_topic, module_type::E_RADAR,
                       E_EMERGENCY_STOP, E_PULL_OVER);
        });
  }

  IF_DOMAIN_MATCH(FLAGS_tracker_rs_topic) {
    rs_reader_ =
        daemon_monitor_node_
            ->CreateReader<apollo::perception::TransmissionObstacles>(
                FLAGS_tracker_rs_topic,
                [this](const std::shared_ptr<
                       apollo::perception::TransmissionObstacles>& msg) {
                  RsStatusCheck(msg);
                  if (nullptr != msg && 0 != msg->rs_sdk_status()) {
                    MsgCheckFunc(msg, FLAGS_tracker_rs_topic, module_type::E_RS,
                                 E_EMERGENCY_STOP, E_PULL_OVER);
                  }
                });
  }

  IF_DOMAIN_MATCH(FLAGS_tracker_ultrasonic_topic) {
    ultrasonic_reader_ =
        daemon_monitor_node_
            ->CreateReader<apollo::perception::TransmissionObstacles>(
                FLAGS_tracker_ultrasonic_topic,
                [this](const std::shared_ptr<
                       apollo::perception::TransmissionObstacles>& msg) {
                  MsgCheckFunc(msg, FLAGS_tracker_ultrasonic_topic,
                               module_type::E_ULTRASONIC, E_EMERGENCY_STOP,
                               E_PULL_OVER);
                });
  }

  IF_DOMAIN_MATCH(FLAGS_tracker_camera_topic) {
    camera_reader_ = daemon_monitor_node_->CreateReader<
        apollo::perception::TransmissionObstacles>(
        FLAGS_tracker_camera_topic,
        [this](const std::shared_ptr<apollo::perception::TransmissionObstacles>&
                   msg) {
          MsgCheckFunc(msg, FLAGS_tracker_camera_topic, module_type::E_CAMERA,
                       E_EMERGENCY_STOP, E_PULL_OVER);
        });
  }
}

void DaemonMonitor::PlanningCheck(const auto& msg) {
  auto status = msg->header().status().error_code();
  if (apollo::common::ErrorCode::PLANNING_ERROR_NEED_RESTART != status) {
    planning_abnormal_timestamp_ = 0.0;
    return;
  }

  double timestamp = apollo::cyber::Clock::NowInSeconds();
  if (planning_abnormal_timestamp_ < kMininumTime) {
    planning_abnormal_timestamp_ = timestamp;
    return;
  }

  if (std::abs(timestamp - planning_abnormal_timestamp_) >
      FLAGS_kill_planning_time_diff) {
    AINFO << "kill planning process,last PLANNING_ERROR_NEED_RESTART time:"
          << planning_abnormal_timestamp_ << ",now time=" << timestamp;
    planning_abnormal_timestamp_ = 0.0;
    KillProcessCmd("planning.dag");
  }
}

void DaemonMonitor::RsStatusCheck(const auto& msg) {
  int rs_sdk_status = msg->rs_sdk_status();
  if (0 == rs_sdk_status) {
    double timestamp = apollo::cyber::Clock::NowInSeconds();
    if (std::abs(timestamp - localization_time_stamp_) > kDoubleLocRsDiffTime) {
      return;
    }

    if (++rs_sdk_status_zero_count_ >= FLAGS_rs_sdk_status_zero_count) {
      AddedErrorInfo(FLAGS_tracker_rs_topic, E_RS_SDK_STATUS_ZERO, E_PULL_OVER,
                     timestamp);
      AWARN << "rs_sdk_status_zero_count:" << rs_sdk_status_zero_count_;
    }

    if (kill_process_timestamp_ > 0.0) {
      if (timestamp - kill_process_timestamp_ > FLAGS_kill_process_time_diff) {
        AINFO << "kill ros sdk process,last kill process time:"
              << kill_process_timestamp_
              << ",now time=" << std::to_string(timestamp);
        KillProcessCmd("rs_sdk_demo");
        kill_process_timestamp_ = timestamp;
      }
      return;
    }

    if (rs_zero_status_timestamp_ < kDoubleEpsilon) {
      rs_zero_status_timestamp_ = timestamp;
    }

    if (timestamp - rs_zero_status_timestamp_ > FLAGS_kill_ros_sdk_time_diff) {
      AINFO << "kill ros sdk process,zero status  time:"
            << rs_zero_status_timestamp_
            << ",now time=" << std::to_string(timestamp);
      KillProcessCmd("rs_sdk_demo");
      kill_process_timestamp_ = timestamp;
    }
  } else {
    kill_process_timestamp_ = 0.0;
    rs_zero_status_timestamp_ = 0.0;
    rs_sdk_status_zero_count_ = 0;
  }
}

void DaemonMonitor::LocationStatusCheck(const auto& msg) {
  std::string localization_topic = FLAGS_localization_topic;
#ifndef __aarch64__
  localization_topic += "_tcp";
#endif  
  double pose_status = msg->pose().position().y();
  int rs_localization_status = msg->rs_loc_status().rs_localization_status();
  if (pose_status < kDoubleEpsilon ||
      apollo::localization::RS_LOC_LOST == rs_localization_status) {
    double timestamp = apollo::cyber::Clock::NowInSeconds();
    if (abs(timestamp - kill_process_timestamp_) <
        kDoubleLastKillTimeDiffNowTime) {
      return;
    }
    if (localization_abnormal_status_timestamp_ < kDoubleEpsilon) {
      localization_abnormal_status_timestamp_ = timestamp;
    }
    std::string specific_abnormal_loc_name =
        rs_localization_status == apollo::localization::RS_LOC_LOST
            ? "localzation lost"
            : "y = 0";
    if (timestamp - localization_abnormal_status_timestamp_ >
        FLAGS_loc_abnormal_status_time) {
      AddedErrorInfo(localization_topic, E_LOCALIZATION_STATUS_ABNORMAL,
                     E_EMERGENCY_STOP, timestamp);
      AINFO << "kill location process, reason:" << specific_abnormal_loc_name
            << ", time:"
            << std::to_string(localization_abnormal_status_timestamp_)
            << ",now time=" << std::to_string(timestamp);
      KillProcessCmd("rs_sdk_demo");
      localization_abnormal_status_timestamp_ = 0.0;
      kill_process_timestamp_ = timestamp;
    }
  } else {
    localization_abnormal_status_timestamp_ = 0.0;
  }
}

void DaemonMonitor::CheckModuleChannels() {
  // This segment will be triggered per second, (1/s)
  double timestamp = apollo::cyber::Clock::NowInSeconds();
  error_info info;
  info.duration_second = 0;
  info.timestamp = timestamp;
  for (auto& channel_freq : channel_freqs_) {
    auto freq = channel_freq.second.load();
    auto channel = channel_freq.first;
    info.level = E_EMERGENCY_STOP;
    info.domain = monitor_configs_[channel].domain;
    info.module = monitor_configs_[channel].module;
    if (channel == FLAGS_imu_raw_topic) {
      info.level = E_WARNING;
    }
    if (std::abs(freq.GetKey()) < kMininumTime) {
      SetErrorInFoCodeMsg(&info, channel_to_error_[channel_freq.first]);
      if (channel_to_switch_flag_[channel]) {
        std::lock_guard<std::mutex> lock(*mutex_error_code);
        error_info_recorder_.emplace(info.error, info);
      }
      AERROR << channel_freq.first << " not enabled";
      continue;
    }
    GetSuitableDiffTimeThreshold(channel);
    double msg_frame_time = frame_time_current_[channel].ToSecond();
    auto time_diff = std::abs(timestamp - msg_frame_time);
    if (time_diff < suitable_diff_time_threshold_) {
      AINFO << channel_freq.first
            << " : normal current time = " << std::to_string(timestamp)
            << ", channel time = " << std::to_string(freq.GetKey())
            << ", msg_frame time = " << std::to_string(msg_frame_time)
            << ", channel cnt = " << std::to_string(freq.GetValue())
            << ", time diff = " << time_diff << ", suitable_diff ="
            << std::to_string(suitable_diff_time_threshold_);
    } else {
      SetErrorInFoCodeMsg(&info, channel_to_error_[channel_freq.first]);
      if (channel_to_switch_flag_[channel]) {
        std::lock_guard<std::mutex> lock(*mutex_error_code);
        error_info_recorder_.emplace(info.error, info);
      }
      AERROR << channel_freq.first
             << " : abnormal current time = " << std::to_string(timestamp)
             << ", channel time = " << std::to_string(freq.GetKey())
             << ", msg_frame time = " << std::to_string(msg_frame_time)
             << ", channel cnt = " << std::to_string(freq.GetValue())
             << ", time diff = " << time_diff << ", suitable_diff ="
             << std::to_string(suitable_diff_time_threshold_);
    }
  }
}

void DaemonMonitor::GetSystemMonitorData() {
  auto* system_monitor_data = monited_data_->mutable_system_monitor_data();
  system_monitor_data->CopyFrom(*system_monitor_data_);
}

void DaemonMonitor::CheckMapConfigInfo() {
  monitor_reader_->Observe();
  auto monitor_info = monitor_reader_->GetLatestObserved();
  if (nullptr != monitor_info) {
    auto aarch64 = monitor_info->system_monitor_data().ota_package_data();
    auto x86 = system_monitor_data_->ota_package_data();
    if (aarch64.map_file() != x86.map_file() ||
        aarch64.base_map() != x86.base_map() ||
        aarch64.sim_map() != x86.sim_map() ||
        aarch64.routing_map() != x86.routing_map()) {
      auto fault_info = monited_data_->add_fault_data();
      SetFaultDataCodeMsg(fault_info, E_DIFF_OF_MAP);
      fault_info->set_level(E_PULL_OVER);
      fault_info->set_domain_type(DOMAIN_TYPE);
      fault_info->set_module_type(module_type::E_SYSTEM);
    }
  }
}

void DaemonMonitor::GetRunningStatus() {
  auto* running_status = monited_data_->mutable_running_status();

  if (nullptr != mcloud_reader_) {
    mcloud_reader_->Observe();
    auto mcloud_info = mcloud_reader_->GetLatestObserved();
    if (nullptr != mcloud_info && mcloud_info->has_auto_driving_status()) {
      running_status->set_auto_driving_status(
          mcloud_info->auto_driving_status());
    }
  }

  if (nullptr != planning_reader_) {
    planning_reader_->Observe();
    auto trajectory = planning_reader_->GetLatestObserved();
    if (nullptr != trajectory && trajectory->has_trajectory_scenario()) {
      running_status->set_trajectory_scenario(
          trajectory->trajectory_scenario());
    }
  }
}

void DaemonMonitor::GetSystemStatusFaultData() {
  // if (DOMAIN_TYPE != common::DomainType::X86) {
  //   return;  // orin system resources have been overloaded, no need to
  //   monitor
  // }
  auto& system_data = system_monitor_data_->system_data();
  double cpu_usage = system_data.cpu_usage();
  double disk_usage = system_data.disk_usage();
  double mem_usage = system_data.mem_usage();

  if (cpu_usage > FLAGS_cputhreshold) {
    auto fault_info = monited_data_->add_fault_data();
    SetFaultDataCodeMsg(fault_info, E_OUT_OF_CPU);
    fault_info->set_level(E_WARNING);
    fault_info->set_domain_type(DOMAIN_TYPE);
    fault_info->set_module_type(module_type::E_SYSTEM);
  }
  if (disk_usage > FLAGS_diskthreshold) {
    auto fault_info = monited_data_->add_fault_data();
    SetFaultDataCodeMsg(fault_info, E_OUT_OF_DISK);
    fault_info->set_level(E_WARNING);
    fault_info->set_domain_type(DOMAIN_TYPE);
    fault_info->set_module_type(module_type::E_SYSTEM);
  }
  if (mem_usage > FLAGS_memthreshold) {
    auto fault_info = monited_data_->add_fault_data();
    SetFaultDataCodeMsg(fault_info, E_OUT_OF_MEM);
    fault_info->set_level(E_WARNING);
    fault_info->set_domain_type(DOMAIN_TYPE);
    fault_info->set_module_type(module_type::E_SYSTEM);
  }
  if (disk_usage > FLAGS_diskwarningthreshold &&
      disk_usage < FLAGS_diskthreshold) {
    auto fault_info = monited_data_->add_fault_data();
    SetFaultDataCodeMsg(fault_info, E_LOW_DISK);
    fault_info->set_level(E_INFO);
    fault_info->set_domain_type(DOMAIN_TYPE);
    fault_info->set_module_type(module_type::E_SYSTEM);
  }
}

void DaemonMonitor::CheckPredictionStatus() {
  double perception_cur_time =
      frame_time_current_[FLAGS_perception_obstacle_topic].ToSecond();
  if (abs(perception_cur_time - cur_time_second_) >
      kDoublePerceptionDiffCurTime) {
    return;
  }
std::string prediction_topic = FLAGS_prediction_topic;
#ifndef __aarch64__
  prediction_topic += "_tcp";
#endif  
  double perception_prediction_diff =
      abs((frame_time_current_[FLAGS_perception_obstacle_topic] -
           frame_time_current_[prediction_topic])
              .ToSecond());
  if (perception_prediction_diff > FLAGS_prediction_block_diff) {
    ++prediction_status_check_.kill_flag;
    double time_skip =
        cur_time_second_ - prediction_status_check_.kill_prediction_timestamp;
    if (prediction_status_check_.kill_flag > kCacheFrameCount &&
        time_skip > FLAGS_kill_prediction_time_diff) {
      AINFO << "kill prediction:kill flag="
            << prediction_status_check_.kill_flag
            << ",kill prediction timestamp="
            << std::to_string(
                   prediction_status_check_.kill_prediction_timestamp);
      KillProcessCmd("prediction.dag");
      prediction_status_check_.kill_prediction_timestamp = cur_time_second_;
      prediction_status_check_.kill_flag = 0;
    }
  } else {
    prediction_status_check_.kill_flag = 0;
  }
}

void DaemonMonitor::CheckCenterPointStatus() {
  std::string localization_topic = FLAGS_localization_topic;
#ifndef __aarch64__
  localization_topic += "_tcp";
#endif  
  double location_cur_time =
      frame_time_current_[localization_topic].ToSecond();
  double rs_cur_time = frame_time_current_[FLAGS_tracker_rs_topic].ToSecond();
  double track_camera_cur_time =
      frame_time_current_[FLAGS_tracker_camera_topic].ToSecond();
  if (nullptr != mcloud_reader_) {
    mcloud_reader_->Observe();
    auto mcloud_info = mcloud_reader_->GetLatestObserved();
    if (nullptr != mcloud_info && mcloud_info->has_auto_driving_status()) {
      if (!mcloud_info->auto_driving_status()) {
        return;
      }
    }
  }
  if (abs(track_camera_cur_time - cur_time_second_) >
          kDoubleModuleDiffCurTime ||
      abs(location_cur_time - cur_time_second_) > kDoubleModuleDiffCurTime) {
    return;
  }
  if (abs(rs_cur_time - cur_time_second_) < kDoubleModuleDiffCurTime) {
    kill_centerpoint_timestamp_ = 0.0;
    return;
  }
  if (kill_centerpoint_timestamp_ < kMininumTime) {
    kill_centerpoint_timestamp_ = cur_time_second_;
    return;
  }
  double rs_cur_time_diff = cur_time_second_ - kill_centerpoint_timestamp_;
  if (rs_cur_time_diff > FLAGS_kill_centerpoint_diff) {
    KillProcessCmd(FLAGS_rs_module_name);
    AINFO << FLAGS_rs_module_name << " rs_cur_time_diff=" << rs_cur_time_diff
          << "kill timestamp = " << std::to_string(cur_time_second_);
    kill_centerpoint_timestamp_ = 0.0;
  }
}

void DaemonMonitor::CheckRsSdkHanging() {
  static double check_time = 0.0;
  // localization received abnormal and exceeded period
  if (std::fabs(cur_time_second_ - localization_time_stamp_) > k2Sencons &&
      cur_time_second_ - check_time > kRsSdkHangingCheckPeriod) {
    check_time = cur_time_second_;
    system("bash /apollo/scripts/rs_sdk_hanging_check.sh &");
    AINFO << "exec rs_sdk_hanging_check:" << std::to_string(cur_time_second_);
  }
}

void DaemonMonitor::DeamonMonitorStatus() {
  // 1. check some process  running status
  if (DOMAIN_TYPE == common::DomainType::AARCH64) {
    CheckPredictionStatus();
    CheckCenterPointStatus();
    CheckRsSdkHanging();
  }

  // 2. added error_info :check channel enable status
  CheckModuleChannels();
  // 2.1 added error_info : some runtime checks
  if (DOMAIN_TYPE == common::DomainType::AARCH64) {
    CheckLocalizationInitializing();
  } else if (DOMAIN_TYPE == common::DomainType::X86) {
    CheckTransmissionLatency();
  }

  // 3.construct fault_data
  GetMonitedDataFaultData();
  GetSystemStatusFaultData();
  // 3.1 do some special checks and add fault_data
  if (DOMAIN_TYPE == common::DomainType::X86) {
    CheckMapConfigInfo();
    CheckDiskWritableStatus();
  }
  // 4.construct system_monitor_data
  GetSystemMonitorData();

  // 5.construct running_status
  if (DOMAIN_TYPE == common::DomainType::X86) {
    GetRunningStatus();
  }
  // 6.pub monited_data
  PublishMonitedData();
}

void DaemonMonitor::PublishMonitedData() {
  if (FLAGS_turn_on_monitor) {
    common::util::FillHeader(daemon_monitor_node_->Name(), monited_data_.get());
    monited_data_->set_domain_type(DOMAIN_TYPE);
    monitor_writer_->Write(monited_data_);
  }
}

void DaemonMonitor::GetMonitedDataFaultData() {
  monited_data_->clear_fault_data();
  {
    std::lock_guard<std::mutex> lock(*mutex_error_code);
    for (auto error : error_info_recorder_) {
      auto fault_info = monited_data_->add_fault_data();
      fault_info->set_code(error.second.error);
      fault_info->set_code_msg(error.second.msg);
      fault_info->set_level(error.second.level);
      fault_info->set_domain_type(error.second.domain);
      fault_info->set_module_type(error.second.module);
    }
    error_info_recorder_.clear();
  }
}

void DaemonMonitor::RunOnce(const double current_time) {
#if 0  // no in use
  // Get running processes.
  std::vector<std::string> running_processes;
  for (const auto& cmd_file : cyber::common::Glob("/proc/*/cmdline")) {
    // Get process command string.
    std::string cmd_string;
    if (cyber::common::GetContent(cmd_file, &cmd_string) &&
        !cmd_string.empty()) {
      // In /proc/<PID>/cmdline, the parts are separated with \0, which will
      // be converted back to whitespaces here.
      std::replace(cmd_string.begin(), cmd_string.end(), '\0', ' ');
      running_processes.push_back(cmd_string);
    }
  }
#endif
  cur_time_second_.store(cyber::Time().Now().ToSecond());
  // monitor cpu、mem、disk
  system_monitor_->RunOnce(current_time);
  system_monitor_data_ = system_monitor_->GetSystemMonitorData();

  DeamonMonitorStatus();
}

void DaemonMonitor::UpdateStatus(
    const std::vector<std::string>& running_processes,
    const apollo::dreamview::ProcessMonitorConfig& config,
    ComponentStatus* const status) {}

int DaemonMonitor::GetModifiedFilesWithin(const std::string& path, int times,
                                          const std::string& exclude) const {
  int count = 0;

  boost::filesystem::path dir_path(path);
  if (!boost::filesystem::is_directory(dir_path)) {
    AERROR << dir_path << " is not a directory";
    return count;
  }

  auto now = floor(cyber::Clock::NowInSeconds());
  for (const auto& path : boost::filesystem::directory_iterator(dir_path)) {
    if (!boost::filesystem::is_regular_file(path.status())) {
      continue;
    }
    if (!exclude.empty() && path.path().string() == exclude) {
      continue;
    }
    auto last_modified = boost::filesystem::last_write_time(path.path());
    auto diff = now - last_modified;
    if (diff > times) {
      continue;
    }
    ++count;
  }
  return count;
}

bool DaemonMonitor::GetCyberRecorderWriteStatus() {
  std::string process_name = R"("cyber_recorder record")";
  std::string cmd = "ps aux | grep " + process_name + " | grep -v grep";
  int result = system(cmd.c_str());
  if (0 != result) {
    AERROR << "cyber_recorder not run";
    return false;
  }

  int count = GetModifiedFilesWithin(kBagPath, kDiskCheckInterval);
  if (0 == count) {
    AERROR << "cyber_recorder write error";
    return false;
  }
  return true;
}

bool DaemonMonitor::GetLogWriteStatus() {
  int count =
      GetModifiedFilesWithin(kLogPath, kDiskCheckInterval, kWirteTestFilePath);
  if (0 == count) {
    AERROR << "log write error";
    return false;
  }
  return true;
}

bool DaemonMonitor::GetDiskWriteStatus() {
  std::ofstream outputFile(kWirteTestFilePath);
  if (outputFile.fail()) {
    AERROR << "Error opening file";
    return false;
  }
  outputFile << "test wirte disk!";
  if (outputFile.fail()) {
    AERROR << "Error writing to file";
    return false;
  }
  outputFile.close();
  return true;
}

void DaemonMonitor::CheckDiskWritableStatus() {
  auto now = floor(cyber::Clock::NowInSeconds());
  if (last_check_disk_time_ < kMininumTime) {
    last_check_disk_time_ = now;
  }

  if (now - last_check_disk_time_ < kDoubleDiskCheckFreq) {
    return;
  }

  last_check_disk_time_ = now;

  bool disk_status = GetDiskWriteStatus();
  bool cyber_recorder_status = GetCyberRecorderWriteStatus();
  bool log_status = GetLogWriteStatus();

  if (!cyber_recorder_status) {
    auto fault_info = monited_data_->add_fault_data();
    SetFaultDataCodeMsg(fault_info, E_CYBER_RECORDER_WRITE_STATUS);
    fault_info->set_level(E_WARNING);
    fault_info->set_domain_type(DOMAIN_TYPE);
    fault_info->set_module_type(module_type::E_SYSTEM);
  }

  if (!log_status) {
    auto fault_info = monited_data_->add_fault_data();
    SetFaultDataCodeMsg(fault_info, E_LOG_WRITE_STATUS);
    fault_info->set_level(E_WARNING);
    fault_info->set_domain_type(DOMAIN_TYPE);
    fault_info->set_module_type(module_type::E_SYSTEM);
  }

  if (!disk_status) {
    auto fault_info = monited_data_->add_fault_data();
    SetFaultDataCodeMsg(fault_info, E_DISK_WRITE_STATUS);
    fault_info->set_level(E_WARNING);
    fault_info->set_domain_type(DOMAIN_TYPE);
    fault_info->set_module_type(module_type::E_SYSTEM);
  }
}

bool DaemonMonitor::KillProcessCmd(const std::string& process_name) {
  std::stringstream ss;
  ss << "ps -ef | grep '" << process_name
     << "' |grep -v grep |awk '{print $2}'|"
     << "xargs kill -9";
  AINFO << "kill process cmd:" << ss.str();
  system(ss.str().c_str());
  return true;
}

bool DaemonMonitor::CheckLocalNetwork(const std::string& local_ip) const {
  std::stringstream ss;
  ss << "ifconfig | grep 'inet ' | awk '{print $2}' | grep '" << local_ip
     << "'";

  int ret = system(ss.str().c_str());
  if (0 != ret) {
    AERROR << "local network down";
    return false;
  }
  return true;
}

bool DaemonMonitor::HostReachable(const std::string& address) const {
  std::stringstream ss;
  ss << "ping -c 1 -W 1 " << address << " 2>&1";

  int ret = system(ss.str().c_str());
  if (0 != ret) {
    AERROR << "address " << address << " not reach";
    return false;
  }

  return true;
}

errorcode DaemonMonitor::CheckNetwork() {
  if (!CheckLocalNetwork(FLAGS_local_address)) {
    AERROR << "local network error";
    return E_NETDOWN;
  }

  if (!HostReachable(FLAGS_outside_address)) {
    AERROR << "Error reaching external network";
    return E_NETNOTREACH;
  }

  if (!HostReachable(FLAGS_tbox_address)) {
    AERROR << "Error reaching box";
    return E_NETNOTREACH;
  }

  if (!HostReachable(FLAGS_other_machine_address)) {
    AERROR << "Error reaching other machine";
    return E_NETNOTREACH;
  }
  return E_OK;
}

void DaemonMonitor::GetSuitableDiffTimeThreshold(const std::string& channel) {
#ifdef __aarch64__  
  if (planning_in_teb_ && channel == FLAGS_planning_trajectory_topic + "_tcp") {
#else
  if (planning_in_teb_ && channel == FLAGS_planning_trajectory_topic) {
#endif   
    double freq = FLAGS_teb_planning_required_frq * FLAGS_lowfreqthreshold;
    suitable_diff_time_threshold_ = std::abs(freq) < kMiniFrequencyEpsilon
                                        ? (1 / FLAGS_teb_planning_required_frq)
                                        : (1 / freq);
    return;
  }
  if (monitor_configs_[channel].threshold_freq > kMiniFrequencyEpsilon) {
    auto reciprocal_data = 1 / monitor_configs_[channel].threshold_freq;
    auto twice_reciprocal_data = reciprocal_data * 2;
    if (twice_reciprocal_data > kDiffTimeThreshold) {
      suitable_diff_time_threshold_ =
          static_cast<uint16_t>(twice_reciprocal_data);
      return;
    }
  }
  suitable_diff_time_threshold_ = kDiffTimeThreshold;
}

bool DaemonMonitor::MatchLocalizationLogFile(
    const std::string& command,
    std::unordered_set<std::string>* last_init_msg_ptr) {
  last_init_msg_ptr->clear();
  FILE* pipe = popen(command.c_str(), "r");
  if (!pipe) {
    AERROR << "Failed to execute command" << command;
    return false;
  }
  char buffer[kMaxStringLenth];
  while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
    buffer[strlen(buffer) - 1] = '\0';
    AWARN << "localization log file matched ,[" << buffer << "]";
    last_init_msg_ptr->emplace(buffer);
  }
  pclose(pipe);

  return true;
}

void DaemonMonitor::CheckLocalizationInitializing() {
  static std::unordered_map<std::string, errorcode> log_msg_to_errorcode = {
      {"IMU", E_LOC_INIT_IMU_WAITING},
      {"GPS/RTK", E_LOC_INIT_GPS_WAITING},
      {"Odometry", E_LOC_INIT_ODOMETRY_WAITING},
      {"RS-LiDAR", E_LOC_INIT_LIDAR_WAITING},
      {"KEY_NOT_FOUND", E_LOC_INIT_KEY_NOT_FOUND},
  };

  static int32_t keep_times = 0;
  static std::unordered_set<std::string> sensor_waiting;
  static std::unordered_set<std::string> key_not_found;

  // localization init finished
  if (std::fabs(cur_time_second_ - localization_time_stamp_) < k2Sencons) {
    return;
  }

  // update init status every 4 times. No need for every time.
  ++keep_times;
  if (FLAGS_local_init_keep_times - 1 == keep_times) {
    // check  key_not_found
    MatchLocalizationLogFile(FLAGS_localization_init_check_key_command,
                             &key_not_found);
  } else if (FLAGS_local_init_keep_times == keep_times) {
    // check sensor init waiting
    MatchLocalizationLogFile(FLAGS_localization_init_check_command,
                             &sensor_waiting);
  } else if (FLAGS_local_init_keep_times < keep_times) {
    keep_times = 0;
  }
  std::string localization_topic = FLAGS_localization_topic;
#ifndef __aarch64__
  localization_topic += "_tcp";
#endif  
  for (auto& msg : key_not_found) {
    if (0 == log_msg_to_errorcode.count(msg)) {
      AERROR << msg << " not support,command:"
             << FLAGS_localization_init_check_key_command;
    } else {
      AddedErrorInfo(localization_topic, log_msg_to_errorcode[msg],
                     E_PULL_OVER, cur_time_second_);
    }
  }

  for (auto& msg : sensor_waiting) {
    if (0 == log_msg_to_errorcode.count(msg)) {
      AERROR << msg << " not support,command:"
             << FLAGS_localization_init_check_command;
    } else {
      AddedErrorInfo(localization_topic, log_msg_to_errorcode[msg],
                     E_PULL_OVER, cur_time_second_);
    }
  }

  return;
}

void DaemonMonitor::JudgeTransimissionLatency(const double timestamp_sec) {
  double cur_timestamp = apollo::cyber::Time::Now().ToSecond();
  double time_diff = std::fabs(timestamp_sec - cur_timestamp);
  if (time_diff < FLAGS_transmission_latency_diff) {
    transmission_latency_start_.store(0.0);  // normal
  } else if (transmission_latency_start_.load() < kMininumTime) {
    transmission_latency_start_.store(cur_timestamp);
    AWARN << "transmission_latency_start_ time_diff "
          << std::to_string(time_diff);
  }
}

void DaemonMonitor::CheckTransmissionLatency() {
  if (transmission_latency_start_.load() > kMininumTime) {
    if (cur_time_second_ - transmission_latency_start_.load() >
        kTransmissionLatencyDuration) {
      AWARN << "transmission_latency_start_ "
            << std::to_string(transmission_latency_start_.load());

      if (!FLAGS_transmission_latency_switch) {
        return;
      }
      error_info info;
      info.module = module_type::E_SYSTEM;
      info.level = E_PULL_OVER;
      info.duration_second = 0;
      info.timestamp = static_cast<uint64_t>(cur_time_second_);
      info.domain = common::DomainType::X86;
      SetErrorInFoCodeMsg(&info, E_EXCEEDING_TIME_DIFF);
      {
        std::lock_guard<std::mutex> lock(*mutex_error_code);
        error_info_recorder_.emplace(info.error, info);
      }
    }
  }
}
}  // namespace monitor
}  // namespace apollo
