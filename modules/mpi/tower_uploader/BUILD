load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")

package(default_visibility = ["//visibility:public"])

apollo_cc_library(
    name = "tower_general_def",
    hdrs = ["tower_general_def.h"],
)

apollo_cc_library(
    name = "tower_base",
    hdrs = ["tower_base.h"],
    srcs = ["tower_base.cc"],
    deps = [
        ":tower_general_def",
        "//cyber/common:cyber_common",
        "//modules/common/configs:common_def",
        "@com_github_jbeder_yaml_cpp//:yaml-cpp",
    ] 
    # + select({
    #     "@platforms//cpu:x86_64": ["@curl_x86//:curl"],
    #     "@platforms//cpu:aarch64": ["@curl_aarch64//:curl"],
    #     "//conditions:default": ["@curl_x86//:curl"]
    # })
)

apollo_cc_library(
    name = "tower_token_generator",
    hdrs = ["tower_token_generator.h"],
    srcs = ["tower_token_generator.cc"],
    deps = [
        ":tower_base",
        "//cyber/common:cyber_common",
        "@com_github_nlohmann_json//:json",
        "@com_github_jbeder_yaml_cpp//:yaml-cpp"
    ]
)

apollo_cc_library(
    name = "tower_uploader",
    hdrs = ["tower_uploader.h"],
    srcs = ["tower_uploader.cc"],
    deps = [
        ":tower_base",
        ":tower_token_generator",
        "//cyber/common:cyber_common",
        "@com_github_nlohmann_json//:json",
    ]
)

# apollo_cc_binary(
#     name = "tower_upload_example",
#     srcs = ["tower_upload_example.cc"],
#     deps = [
#         ":tower_uploader",
#     ]
# )

apollo_package()
cpplint()
