checker_config: {

}

collector_config: {
  white_channel: {
    channels: "imu_raw"
    channels: "/apollo/camera/front_12mm/status"
    channels: "/apollo/camera/front_3mm/status"
    channels: "/apollo/canbus/chassis"
    channels: "/apollo/control"
    channels: "/apollo/localization/pose_tcp"
    channels: "/apollo/mcloud"
    channels: "/apollo/mcloud/super_traffic_light"
    channels: "/apollo/monitor/monitor_data_aarch"
    channels: "/apollo/monitor/monitor_data_x86"
    channels: "/apollo/perception/obstacles"
    channels: "/apollo/perception/traffic_light_tcp"
    channels: "/apollo/planning"
    channels: "/apollo/prediction_tcp"
    channels: "/apollo/robosense/obstacles"
    channels: "/apollo/routing_request"
    channels: "/apollo/routing_response"
    channels: "/apollo/routing_result"
    channels: "/apollo/sensor/radar"
    channels: "/apollo/sensor/udas_ultrasonic"
    channels: "/apollo/tracker/camera"
    channels: "/apollo/tracker/camera_front_3mm"
    channels: "/apollo/tracker/mems"
    channels: "/apollo/tracker/radar"
    channels: "/apollo/tracker/rs"
    channels: "/apollo/tracker/ultrasonic"
    channels: "/tf"
    channels: "/tf_static"
  }

  record_path: "/apollo/data/bag/mpi"
}
