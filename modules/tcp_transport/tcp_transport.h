/******************************************************************************
 * Copyright 2023 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#ifndef CYBER_TRANSPORT_TCP_TRANSPORT_H_
#define CYBER_TRANSPORT_TCP_TRANSPORT_H_

#include <atomic>
#include <map>
#include <memory>
#include <string>
#include <thread>

#include "cyber/cyber.h"
#include "cyber/message/message_traits.h"
#include "cyber/time/rate.h"
#include "cyber/time/time.h"
#include "cyber/transport/dispatcher/shm_dispatcher.h"
#include "modules/common_msgs/planning_msgs/planning.pb.h"
#include "modules/common_msgs/control_msgs/control_cmd.pb.h"
#include "modules/common_msgs/monitor_msgs/system_status.pb.h"
#include "modules/common_msgs/mcloud_msgs/mcloud_info.pb.h"
#include "modules/common_msgs/mcloud_msgs/location_point_init.pb.h"
#include "modules/common_msgs/mcloud_msgs/super_traffic_light.pb.h"
#include "modules/common_msgs/mcloud_msgs/v2x_traffic_light.pb.h"
#include "modules/common_msgs/localization_msgs/localization.pb.h"
#include "modules/common_msgs/prediction_msgs/prediction_obstacle.pb.h"
#include "modules/common_msgs/perception_msgs/traffic_light_detection.pb.h"
#include "modules/common_msgs/chassis_msgs/chassis.pb.h"

namespace apollo {
namespace tcp_transport {

using cyber::common::GlobalData;
using apollo::cyber::Rate;
using apollo::cyber::Time;
using apollo::cyber::message::RawMessage;
using apollo::cyber::proto::RoleAttributes;
using apollo::cyber::transport::MessageInfo;
using apollo::cyber::transport::ReadableBlock;
using apollo::cyber::transport::ShmDispatcher;
using apollo::planning::ADCTrajectory;
using apollo::control::ControlCommand;
using apollo::monitor::MonitoredData;
using apollo::mcloud::McloudInfo;
using apollo::mcloud::LocationPointInit;
using apollo::mcloud::SuperTrafficLight;
using apollo::mcloud::V2XTrafficLightList;
using apollo::localization::LocalizationEstimate;
using apollo::prediction::PredictionObstacles;
using apollo::perception::TrafficLightDetection;
using apollo::perception::PerceptionObstacles;
using apollo::canbus::Chassis;

using ReaderPtrMap =
    std::map<uint64_t, std::shared_ptr<apollo::cyber::Reader<RawMessage>>>;
using WriterPtrMap =
    std::map<uint64_t, std::shared_ptr<apollo::cyber::Writer<RawMessage>>>;
enum TransportMode {
  TM_UNKOWN = 0x00,
  TM_PUB = 0x01,   // 0000 0001
  TM_SUB = 0x02,   // 0000 0010
  TM_BOTH = 0x03,  // 0000 0011
};

/*
  * @brief TcpDataHead 是 TCP 数据包的头部结构体
  * 包含消息大小、消息信息大小、通道 ID 和序列号等
  * 用于在 TCP 传输中标识和处理消息
  * @note 序列号使用原子操作生成，确保在多线程环境下的唯一性
  * @note msg_info_size 用于存储消息的附加信息大小
  * @note channel_id 用于标识消息所属的通道
  * @note seq_num 用于标识消息的序列号，确保消息的顺序性
  * @note msg_size 用于存储消息的实际大小
  * @note 该结构体在 TCP 传输中用于解析和处理消息
*/
struct TcpDataHead {
  uint64_t msg_size = 0;
  uint64_t msg_info_size = 0;
  uint64_t channel_id = 0;
  uint64_t seq_num = 0;
  TcpDataHead(uint64_t _msg_size, uint64_t _msg_info_size, uint64_t _channel_id)
      : msg_size(_msg_size),
        msg_info_size(_msg_info_size),
        channel_id(_channel_id) {
    static std::atomic<uint64_t> seq_num_base = {0};
    seq_num = seq_num_base.fetch_add(1);
  }
};

/*
  * @brief LockGuard 是一个简单的锁保护类
  * 用于在多线程环境下保护共享资源的访问
  * 在构造函数中获取锁，在析构函数中释放锁
  * @note 使用 std::atomic_flag 来实现自旋锁
  * @note 在获取锁时，如果锁已被占用，则会进行自旋等待
  * @note 每隔一定时间（100微秒）输出一次等待日志，避免过于频繁的日志输出
*/
class LockGuard {
 public:
  explicit LockGuard(std::atomic_flag &lock) : lock_(lock) {
    uint32_t retry_times = 0;
    while (lock_.test_and_set(std::memory_order_acquire)) {
      if (++retry_times == 100) {
        AWARN << "tcp_buffer_lock_ waiting...";
        std::this_thread::sleep_for(std::chrono::microseconds(100));
        // std::this_thread::yield();
        retry_times = 0;
      }
    }
  }
  ~LockGuard() { lock_.clear(std::memory_order_release); }

 private:
  std::atomic_flag &lock_;
};

class TcpTransport {
 public:
  TcpTransport();
  ~TcpTransport();
  bool Init(std::shared_ptr<apollo::cyber::Node> node,
            TransportMode mode = TM_BOTH);
  void RunThread();

 private:
  bool CheckConfig();
  void Stop();
  bool CreatePublisher();
  bool CreateSubscriber();
  bool CreateReaders();
  bool CreateWriters();

  void PubMsg();
  void PubSingleMsg(char* publiser, std::atomic<int32_t>& tcp_buffer_size, bool& idle);
  void SubMsg();
  
  void PutIntoBuffer(const uint64_t channel_id,
                     const std::shared_ptr<ReadableBlock> &rb,
                     const MessageInfo &msg_info);
  template<typename ProtoType>
  bool ParseTcpBufferToProto(const char* buffer, const TcpDataHead& head, 
                             std::shared_ptr<ProtoType>& proto_msg);

 private:
  std::atomic<bool> shutdown_ = {false};
  std::shared_ptr<apollo::cyber::Node> node_ = nullptr;
  TransportMode mode_ = TM_UNKOWN;
  int pub_fd_ = -1;
  int sub_fd_ = -1;
  ReaderPtrMap tcp_reader_;
  WriterPtrMap tcp_writer_;

  std::unique_ptr<std::thread> pub_thread_ = nullptr;
  std::unique_ptr<std::thread> sub_thread_ = nullptr;

  char *tcp_buffer_sub_ = nullptr;
  //每个话题用不同的buffer
  #if defined(__x86_64__)
  char *tcp_buffer_plan_pub_ = nullptr;
  char *tcp_buffer_control_pub_ = nullptr;
  char *tcp_buffer_monitor_pub_ = nullptr;
  char *tcp_buffer_mcloud_pub_ = nullptr;
  char *tcp_buffer_mcloud_location_point_init_pub_ = nullptr;
  char *tcp_buffer_mcloud_super_traffic_light_pub_ = nullptr;
  char *tcp_buffer_mcloud_v2x_traffic_light_pub_ = nullptr;
  std::atomic<int32_t> tcp_buffer_plan_size_ = {0};
  std::atomic<int32_t> tcp_buffer_control_size_ = {0};
  std::atomic<int32_t> tcp_buffer_monitor_size_ = {0};
  std::atomic<int32_t> tcp_buffer_mcloud_size_ = {0};
  std::atomic<int32_t> tcp_buffer_mcloud_location_point_init_size_ = {0};
  std::atomic<int32_t> tcp_buffer_mcloud_super_traffic_light_size_ = {0};
  std::atomic<int32_t> tcp_buffer_mcloud_v2x_traffic_light_size_ = {0};
  std::shared_ptr<apollo::cyber::Writer<LocalizationEstimate>> localization_pose_writer_;
  std::shared_ptr<apollo::cyber::Writer<PredictionObstacles>> prediction_obs_writer_;
  std::shared_ptr<apollo::cyber::Writer<TrafficLightDetection>> traffic_light_writer_;
  std::shared_ptr<apollo::cyber::Writer<PerceptionObstacles>> perception_obs_writer_;
  std::shared_ptr<apollo::cyber::Writer<Chassis>> chassis_writer_;
  #elif defined(__aarch64__)
  char *tcp_buffer_localization_pub_ = nullptr;
  char *tcp_buffer_prediction_pub_ = nullptr;
  char *tcp_buffer_traffic_light_pub_ = nullptr;
  char *tcp_buffer_perception_obs_pub_ = nullptr;
  char *tcp_buffer_chassis_pub_ = nullptr;
  std::atomic<int32_t> tcp_buffer_localization_size_ = {0};
  std::atomic<int32_t> tcp_buffer_prediction_size_ = {0};
  std::atomic<int32_t> tcp_buffer_traffic_light_size_ = {0};
  std::atomic<int32_t> tcp_buffer_perception_obs_size_ = {0};
  std::atomic<int32_t> tcp_buffer_chassis_size_ = {0};
  std::shared_ptr<apollo::cyber::Writer<ADCTrajectory>> plan_writer_;
  std::shared_ptr<apollo::cyber::Writer<ControlCommand>> control_writer_;
  std::shared_ptr<apollo::cyber::Writer<MonitoredData>> monitor_writer_;
  std::shared_ptr<apollo::cyber::Writer<McloudInfo>> mcloud_writer_;
  std::shared_ptr<apollo::cyber::Writer<LocationPointInit>> mcloud_location_point_init_writer_;
  std::shared_ptr<apollo::cyber::Writer<SuperTrafficLight>> mcloud_super_traffic_light_writer_;
  std::shared_ptr<apollo::cyber::Writer<V2XTrafficLightList>> mcloud_v2x_traffic_light_writer_;
  #endif

  std::atomic_flag tcp_buffer_lock_ = ATOMIC_FLAG_INIT;
  std::atomic<bool> pub_error_ = {false};
  std::atomic<bool> sub_error_ = {false};
  std::string local_ip_ = "";
  std::string remote_ip_ = "";
  std::string default_port_ = "";
};
}  // namespace tcp_transport
}  // namespace apollo

#endif  // CYBER_TRANSPORT_TCP_TRANSPORT_H_
