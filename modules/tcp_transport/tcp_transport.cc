/******************************************************************************
 * Copyright 2023 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#include "modules/tcp_transport/tcp_transport.h"

#include "cyber/trace/trace.h"
#include "cyber/cyber_trace/trace.h"
#include "modules/tcp_transport/common/tt_gflags.h"
#include "third_party/nanomsg/include/nn.h"
#include "third_party/nanomsg/include/pubsub.h"
#include "third_party/nanomsg/include/tcp.h"
#include "third_party/safec/include/safe_mem_lib.h"
namespace apollo {
namespace tcp_transport {

namespace {
constexpr int32_t kDomainIpNumber = 2;
constexpr u_int32_t kBufferSize = 256 * 1024;
constexpr int32_t KRecvTimeOut = 1000;  // ms
}  // namespace


TcpTransport::TcpTransport() {
  #if defined(__x86_64__)
  tcp_buffer_plan_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_plan_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_control_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_control_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_monitor_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_monitor_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_mcloud_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_mcloud_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_mcloud_location_point_init_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_mcloud_location_point_init_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_mcloud_super_traffic_light_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_mcloud_super_traffic_light_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_mcloud_v2x_traffic_light_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_mcloud_v2x_traffic_light_pub_, kBufferSize, 0, kBufferSize);
  #elif defined(__aarch64__)
  tcp_buffer_localization_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_localization_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_prediction_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_prediction_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_traffic_light_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_traffic_light_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_perception_obs_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_perception_obs_pub_, kBufferSize, 0, kBufferSize);
  tcp_buffer_chassis_pub_ = new char[kBufferSize];
  memset_s(tcp_buffer_chassis_pub_, kBufferSize, 0, kBufferSize);
  #endif

  tcp_buffer_sub_ = new char[kBufferSize];
  memset_s(tcp_buffer_sub_, kBufferSize, 0, kBufferSize);

  tcp_reader_.clear();
  tcp_writer_.clear();
}
TcpTransport::~TcpTransport() {
  Stop();
  #if defined(__x86_64__)
  if (nullptr != tcp_buffer_plan_pub_) {
    delete[] tcp_buffer_plan_pub_;
    tcp_buffer_plan_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_control_pub_) {
    delete[] tcp_buffer_control_pub_;
    tcp_buffer_control_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_monitor_pub_) {
    delete[] tcp_buffer_monitor_pub_;
    tcp_buffer_monitor_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_mcloud_pub_) {
    delete[] tcp_buffer_mcloud_pub_;
    tcp_buffer_mcloud_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_mcloud_location_point_init_pub_) {
    delete[] tcp_buffer_mcloud_location_point_init_pub_;
    tcp_buffer_mcloud_location_point_init_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_mcloud_super_traffic_light_pub_) {
    delete[] tcp_buffer_mcloud_super_traffic_light_pub_;
    tcp_buffer_mcloud_super_traffic_light_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_mcloud_v2x_traffic_light_pub_) {
    delete[] tcp_buffer_mcloud_v2x_traffic_light_pub_;
    tcp_buffer_mcloud_v2x_traffic_light_pub_ = nullptr;
  }
  #elif defined(__aarch64__)
  if (nullptr != tcp_buffer_localization_pub_) {
    delete[] tcp_buffer_localization_pub_;
    tcp_buffer_localization_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_prediction_pub_) {
    delete[] tcp_buffer_prediction_pub_;
    tcp_buffer_prediction_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_traffic_light_pub_) {
    delete[] tcp_buffer_traffic_light_pub_;
    tcp_buffer_traffic_light_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_perception_obs_pub_) {
    delete[] tcp_buffer_perception_obs_pub_;
    tcp_buffer_perception_obs_pub_ = nullptr;
  }
  if (nullptr != tcp_buffer_chassis_pub_) {
    delete[] tcp_buffer_chassis_pub_;
    tcp_buffer_chassis_pub_ = nullptr;
  }
  #endif
  if (nullptr != tcp_buffer_sub_) {
    delete[] tcp_buffer_sub_;
    tcp_buffer_sub_ = nullptr;
  }
}

void TcpTransport::Stop() {
  shutdown_.exchange(true, std::memory_order_release);
  ShmDispatcher::Instance(false)->SetTransOutCallback(nullptr);
  if (nullptr != pub_thread_ && pub_thread_->joinable()) {
    pub_thread_->join();
    pub_thread_ = nullptr;
  }
  if (nullptr != sub_thread_ && sub_thread_->joinable()) {
    sub_thread_->join();
    sub_thread_ = nullptr;
  }
  if (nullptr != node_) {
    node_.reset();
  }
}

/*
  * @brief 检查配置是否有效
  * 1. 获取环境变量 CYBER_IP，默认为 127.0.0.1
  * 2. 获取全局配置中的 TCP 传输配置
  * 3. 检查 TCP 传输是否启用
  * 4. 检查域名 IP 数量是否为 2， 即一个本地 IP 和另一个远程 IP
  * 5. 检查域名 IP 是否包含 CYBER_IP
  * 6. 设置本地 IP 和远程 IP
  * 7. 返回配置是否有效
  * @return true 配置有效
  * @return false 配置无效
*/
bool TcpTransport::CheckConfig() {
  std::string cyber_ip("127.0.0.1");
  const char *ip_val = ::getenv("CYBER_IP");
  if (ip_val != nullptr && 0 != *ip_val) {
    cyber_ip = ip_val;
  } else {
    AERROR << "can not get CYBER_IP (? sudo -SE env)";
    return false;
  }
  AINFO << "cyber ip: " << cyber_ip;

  const auto &tcp_transport = GlobalData::Instance()->GetTcpTransport();
  AINFO << tcp_transport.DebugString();
  if (!tcp_transport.globle_enable()) {
    AERROR << "tcp_transport.enable is false, do not need launch program";
    // return false;
  }
  if (kDomainIpNumber != tcp_transport.domain_ips().size()) {
    AERROR << "tcp_transport.domain_ips more than " << kDomainIpNumber;
    return false;
  }
  default_port_ = tcp_transport.default_port();
  if (tcp_transport.domain_ips(0) == cyber_ip) {
    local_ip_ = tcp_transport.domain_ips(0);
    remote_ip_ = tcp_transport.domain_ips(1);
  } else if (tcp_transport.domain_ips(1) == cyber_ip) {
    remote_ip_ = tcp_transport.domain_ips(0);
    local_ip_ = tcp_transport.domain_ips(1);
  } else {
    AERROR << "tcp_transport.domain_ips not include cyber_ip!" << cyber_ip;
    return false;
  }
  return true;
}

bool TcpTransport::Init(std::shared_ptr<apollo::cyber::Node> node,
                        TransportMode mode) {
  mode_ = mode;  // TransportMode
  node_ = node;
  // 检查配置
  if (!CheckConfig()) {
    AERROR << "CheckConfig failed!";
    return false;
  }
  // 初始化共享内存分发器
  if (mode_ & TM_PUB) {
    if (!CreatePublisher()) {
      AERROR << "CreatePublisher failed!";
      return false;
    }
    CreateReaders();
  }
  if (mode_ & TM_SUB) {
    if (!CreateSubscriber()) {
      AERROR << "CreateSubscriber failed!";
      return false;
    }
    CreateWriters();
  }

  return true;
}
bool TcpTransport::CreatePublisher() {
  if (pub_fd_ > 0) {
    nn_close(pub_fd_);
    pub_fd_ = -1;
  }

  pub_fd_ = nn_socket(AF_SP, NN_PUB);
  if (pub_fd_ < 0) {
    AERROR << "nn_socket error:" << nn_strerror(nn_errno());
    return false;
  }

  int val = 1;
  if (nn_setsockopt(pub_fd_, NN_TCP, NN_TCP_NODELAY, &val, sizeof(val))) {
    AERROR << "nn_socket error:" << nn_strerror(nn_errno());
    return false;
  }

  // as server ,publish local channel data to remote
  std::string tcp_url = "tcp://";
  tcp_url += local_ip_ + ":" + default_port_;
  AINFO << "tcp_ip: "<<tcp_url;
  if (nn_bind(pub_fd_, tcp_url.data()) < 0) {
    AERROR << "nn_bind error:" << nn_strerror(nn_errno());
    nn_close(pub_fd_);
    pub_fd_ = -1;
    return false;
  }
  AINFO << "CreatePublisher success";
  return true;
}

bool TcpTransport::CreateSubscriber() {
  if (sub_fd_ > 0) {
    nn_close(sub_fd_);
    sub_fd_ = -1;
  }

  sub_fd_ = nn_socket(AF_SP, NN_SUB);
  if (sub_fd_ < 0) {
    AERROR << "nn_socket error:" << nn_strerror(nn_errno());
    return false;
  }
  /*  We want all messages, so just subscribe to the empty value. */
  if (nn_setsockopt(sub_fd_, NN_SUB, NN_SUB_SUBSCRIBE, "", 0) < 0) {
    AERROR << "nn_setsockopt  error:" << nn_strerror(nn_errno());
    nn_close(sub_fd_);
    sub_fd_ = -1;
    return (-1);
  }

  int32_t timeout = KRecvTimeOut;
  if (nn_setsockopt(sub_fd_, NN_SOL_SOCKET, NN_RCVTIMEO, &timeout,
                    sizeof(timeout)) < 0) {
    AERROR << "nn_setsockopt error:" << nn_strerror(nn_errno());
    nn_close(sub_fd_);
    sub_fd_ = -1;
    return (-1);
  }

  // as client ,subscriber remote channel data  and write to local shm.
  std::string tcp_url = "tcp://";
  tcp_url += remote_ip_ + ":" + default_port_;
  AINFO << "tcp_ip: "<<tcp_url;
  if (nn_connect(sub_fd_, tcp_url.data()) < 0) {
    AERROR << "nn_connect error:" << nn_strerror(nn_errno());
    nn_close(sub_fd_);
    sub_fd_ = -1;
    return false;
  }



  AINFO << "CreateSubscriber success";
  return true;
}

/*
  * @brief 创建读者
  * 1. 设置共享内存分发器的输出回调函数 PutIntoBuffer
  * 2. 获取全局 TCP 传输配置
  * 3. 检查 TCP 传输是否启用
  * 4. 遍历 TCP 通道，创建本地 IP 的读者
  * 5. 将读者存储到 tcp_reader_ 中
  * @return true 成功
  * @return false 失败
*/
bool TcpTransport::CreateReaders() {
  ShmDispatcher::Instance(true)->SetTransOutCallback(
      std::bind(&TcpTransport::PutIntoBuffer, this, std::placeholders::_1,
                std::placeholders::_2, std::placeholders::_3));
  RoleAttributes attr;
  attr.set_tcp_flag(true);

  const auto &tcp_transport = GlobalData::Instance()->GetTcpTransport();
  if (!tcp_transport.globle_enable()) {
    AERROR << "tcp_transport.enable is false, do not need create reader";
    return false;
  }
  for (auto tcp_channel : tcp_transport.tcp_channels()) {
    auto channel_id = GlobalData::RegisterChannel(tcp_channel.channel_name());
    if (tcp_channel.enable() && local_ip_ == tcp_channel.owner_ip()) 
    {
      attr.set_channel_name(tcp_channel.channel_name());
      auto reader = node_->CreateReader<RawMessage>(attr);
      if (nullptr != reader) {
        tcp_reader_[channel_id] = reader;
        AINFO << "create reader,channel_name=" << tcp_channel.channel_name()
              << ",channel_id=" << channel_id;
      }
    }
  }
  AINFO << "CreateReaders success";
  return true;
}

/*
  * @brief 创建写者
  * 1. 设置角色属性的 TCP 标志为 true
  * 2. 获取全局 TCP 传输配置
  * 3. 检查 TCP 传输是否启用
  * 4. 遍历 TCP 通道，创建远程 IP 的写者
  * 5. 将写者存储到 tcp_writer_ 中
  * @return true 成功
  * @return false 失败
*/
bool TcpTransport::CreateWriters() {
  RoleAttributes attr;
  attr.set_tcp_flag(true);

  const auto &tcp_transport = GlobalData::Instance()->GetTcpTransport();
  if (!tcp_transport.globle_enable()) {
    AERROR << "tcp_transport.enable is false, do not need create writer";
    return false;
  }
  for (auto tcp_channel : tcp_transport.tcp_channels()) {
    auto channel_id = GlobalData::RegisterChannel(tcp_channel.channel_name());

    if (tcp_channel.enable() && remote_ip_ == tcp_channel.owner_ip() && 
        tcp_channel.channel_name()!="/apollo/planning" && 
        tcp_channel.channel_name()!="/apollo/control" && 
        tcp_channel.channel_name()!="/apollo/monitor/monitor_data_x86" && 
        tcp_channel.channel_name()!="/apollo/mcloud" &&
        tcp_channel.channel_name()!="/apollo/mcloud/location_point_init" &&
        tcp_channel.channel_name()!="/apollo/mcloud/super_traffic_light" &&
        tcp_channel.channel_name()!="/apollo/mcloud/v2x_traffic_light" &&
        tcp_channel.channel_name()!="/apollo/localization/pose" &&
        tcp_channel.channel_name()!="/apollo/prediction" &&
        tcp_channel.channel_name()!="/apollo/perception/traffic_light" &&
        tcp_channel.channel_name()!="/apollo/perception/obstacles" &&
        tcp_channel.channel_name()!="/apollo/canbus/chassis") 
    {
      attr.set_channel_name(tcp_channel.channel_name());
      auto writer = node_->CreateWriter<RawMessage>(attr);
      if (nullptr != writer) {
        tcp_writer_[channel_id] = writer;
        AINFO << "create writer,channel_name=" << tcp_channel.channel_name()
              << ",channel_id=" << channel_id;
      }
    }
  }
  #if defined(__aarch64__)
    plan_writer_ = node_->CreateWriter<ADCTrajectory>("/apollo/planning_tcp");
    AINFO<<"create writer,channel_name=/apollo/planning_tcp";
    control_writer_ = node_->CreateWriter<ControlCommand>("/apollo/control_tcp");
    AINFO<<"create writer,channel_name=/apollo/control_tcp";
    monitor_writer_ = node_->CreateWriter<MonitoredData>("/apollo/monitor/monitor_data_x86_tcp");
    AINFO<<"create writer,channel_name=/apollo/monitor/monitor_data_x86_tcp";
    mcloud_writer_ = node_->CreateWriter<McloudInfo>("/apollo/mcloud_tcp");
    AINFO<<"create writer,channel_name=/apollo/mcloud_tcp";
    mcloud_location_point_init_writer_ = node_->CreateWriter
              <LocationPointInit>("/apollo/mcloud/location_point_init_tcp");
    AINFO<<"create writer,channel_name=/apollo/mcloud/location_point_init_tcp";
    mcloud_super_traffic_light_writer_ = node_->CreateWriter
              <SuperTrafficLight>("/apollo/mcloud/super_traffic_light_tcp");
    AINFO<<"create writer,channel_name=/apollo/mcloud/super_traffic_light_tcp";
    mcloud_v2x_traffic_light_writer_ = node_->CreateWriter
              <V2XTrafficLightList>("/apollo/mcloud/v2x_traffic_light_tcp");
    AINFO<<"create writer,channel_name=/apollo/mcloud/v2x_traffic_light_tcp";
  #elif defined(__x86_64__)
    localization_pose_writer_ = node_->CreateWriter
                <LocalizationEstimate>("/apollo/localization/pose_tcp");
    AINFO<<"create writer,channel_name=/apollo/localization/pose_tcp";
    prediction_obs_writer_ = node_->CreateWriter
                <PredictionObstacles>("/apollo/prediction_tcp");
    AINFO<<"create writer,channel_name=/apollo/prediction_tcp";
    traffic_light_writer_ = node_->CreateWriter
                <TrafficLightDetection>("/apollo/perception/traffic_light_tcp");
    AINFO<<"create writer,channel_name=/apollo/perception/traffic_light_tcp";
    perception_obs_writer_ = node_->CreateWriter
                <PerceptionObstacles>("/apollo/perception/obstacles_tcp");
    AINFO<<"create writer,channel_name=/apollo/perception/obstacles_tcp";
    chassis_writer_ = node_->CreateWriter
                <Chassis>("/apollo/canbus/chassis_tcp");
    AINFO<<"create writer,channel_name=/apollo/canbus/chassis_tcp";
  #endif

  return true;
}

/*
  * @brief 启动线程
  * 1. 如果模式包含 TM_SUB 且 tcp_writer_ 不为空，则创建订阅线程
  * 2. 如果模式包含 TM_PUB 且 tcp_reader_ 不为空，则创建发布线程
*/
void TcpTransport::RunThread() {
  if ((mode_ & TM_SUB) && tcp_writer_.size() >= 0) {
    sub_thread_.reset(
        new std::thread(&TcpTransport::SubMsg, this));
  }
  if ((mode_ & TM_PUB) && tcp_reader_.size() > 0) {
    pub_thread_.reset(
        new std::thread(&TcpTransport::PubMsg, this));
  }
}

/*
  * @brief 将消息放入缓冲区
  * 1. 检查缓冲区大小是否超过限制
  * 2. 创建 TcpDataHead 头部，包含消息大小、消息信息大小和通道 ID
  * 3. 将头部和消息内容复制到缓冲区
  * 4. 更新缓冲区大小
  * 5. 如果启用追踪，则记录追踪信息（配置文件里是false不启用）
*/
void TcpTransport::PutIntoBuffer(const uint64_t channel_id,
                                 const std::shared_ptr<ReadableBlock> &rb,
                                 const MessageInfo &msg_info) {
  auto msg_size = rb->block->msg_size();
  auto msg_info_size = rb->block->msg_info_size();
  AINFO << "msg channel id: "<< channel_id << ",  msg size: "<<msg_size;
  std::string channel_name = apollo::cyber::common::GlobalData::GetChannelById(channel_id);
  uint32_t buffer_size;
  #if defined(__x86_64__)
  if (channel_name == "/apollo/planning") {
    buffer_size = tcp_buffer_plan_size_.load();
  } else if (channel_name == "/apollo/control") {
    buffer_size = tcp_buffer_control_size_.load();
  } else if (channel_name == "/apollo/monitor/monitor_data_x86") {
    buffer_size = tcp_buffer_monitor_size_.load();
  } else if (channel_name == "/apollo/mcloud") {
    buffer_size = tcp_buffer_mcloud_size_.load();
  } else if (channel_name == "/apollo/mcloud/location_point_init") {
    buffer_size = tcp_buffer_mcloud_location_point_init_size_.load();
  } else if (channel_name == "/apollo/mcloud/super_traffic_light") {
    buffer_size = tcp_buffer_mcloud_super_traffic_light_size_.load();
  } else if (channel_name == "/apollo/mcloud/v2x_traffic_light") {
    buffer_size = tcp_buffer_mcloud_v2x_traffic_light_size_.load();
  } 
  #elif defined(__aarch64__)
  if (channel_name == "/apollo/localization/pose") {
    buffer_size = tcp_buffer_localization_size_.load();
  } else if (channel_name == "/apollo/prediction") {
    buffer_size = tcp_buffer_prediction_size_.load();
  } else if (channel_name == "/apollo/perception/traffic_light") {
    buffer_size = tcp_buffer_traffic_light_size_.load();
  } else if (channel_name == "/apollo/perception/obstacles") {
    buffer_size = tcp_buffer_perception_obs_size_.load();
  } else if (channel_name == "/apollo/canbus/chassis") {
    buffer_size = tcp_buffer_chassis_size_.load();
  }
  #endif
  else {
    AERROR << "Unknown channel: " << channel_name;
    return;
  }
  if (buffer_size + sizeof(TcpDataHead) + msg_size + msg_info_size >
      kBufferSize) {
    AERROR << "msg_size:" << msg_size << ", add msg_info_size:" << msg_info_size
           << ", add tcp_buffer_size_:" << buffer_size
           << " is more than " << kBufferSize;
    return;
  }

  static uint64_t seq_num = 0;
  {
    LockGuard lock_guard(tcp_buffer_lock_);
    // msg_info.channel_id_ not used, maybe it's to save shm
    TcpDataHead head(msg_size, msg_info_size, channel_id);
    #if defined(__x86_64__)
    if (channel_name == "/apollo/planning") {
      memcpy_s(tcp_buffer_plan_pub_ + tcp_buffer_plan_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_plan_pub_ + tcp_buffer_plan_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_plan_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/control") {
      memcpy_s(tcp_buffer_control_pub_ + tcp_buffer_control_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_control_pub_ + tcp_buffer_control_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_control_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/monitor/monitor_data_x86") {
      memcpy_s(tcp_buffer_monitor_pub_ + tcp_buffer_monitor_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_monitor_pub_ + tcp_buffer_monitor_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_monitor_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/mcloud") {
      memcpy_s(tcp_buffer_mcloud_pub_ + tcp_buffer_mcloud_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_mcloud_pub_ + tcp_buffer_mcloud_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_mcloud_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/mcloud/location_point_init") {
      memcpy_s(tcp_buffer_mcloud_location_point_init_pub_ +
                   tcp_buffer_mcloud_location_point_init_size_,
               sizeof(head), &head, sizeof(head));
      memcpy_s(tcp_buffer_mcloud_location_point_init_pub_ +
                   tcp_buffer_mcloud_location_point_init_size_ +
                   sizeof(head),
               msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_mcloud_location_point_init_size_ +=
          sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/mcloud/super_traffic_light") {
      memcpy_s(tcp_buffer_mcloud_super_traffic_light_pub_ +
                   tcp_buffer_mcloud_super_traffic_light_size_,
               sizeof(head), &head, sizeof(head));
      memcpy_s(tcp_buffer_mcloud_super_traffic_light_pub_ +
                   tcp_buffer_mcloud_super_traffic_light_size_ +
                   sizeof(head),
               msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_mcloud_super_traffic_light_size_ +=
          sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/mcloud/v2x_traffic_light") {
      memcpy_s(tcp_buffer_mcloud_v2x_traffic_light_pub_ +
                   tcp_buffer_mcloud_v2x_traffic_light_size_,
               sizeof(head), &head, sizeof(head));
      memcpy_s(tcp_buffer_mcloud_v2x_traffic_light_pub_ +
                   tcp_buffer_mcloud_v2x_traffic_light_size_ + sizeof(head),
               msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_mcloud_v2x_traffic_light_size_ +=
          sizeof(TcpDataHead) + msg_size + msg_info_size;
    }
    #elif defined(__aarch64__)
    if (channel_name == "/apollo/localization/pose") {
      memcpy_s(tcp_buffer_localization_pub_ + tcp_buffer_localization_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_localization_pub_ + tcp_buffer_localization_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_localization_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/prediction") {
      memcpy_s(tcp_buffer_prediction_pub_ + tcp_buffer_prediction_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_prediction_pub_ + tcp_buffer_prediction_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_prediction_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/perception/traffic_light") {
      memcpy_s(tcp_buffer_traffic_light_pub_ + tcp_buffer_traffic_light_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_traffic_light_pub_ + tcp_buffer_traffic_light_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_traffic_light_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/perception/obstacles") {
      memcpy_s(tcp_buffer_perception_obs_pub_ + tcp_buffer_perception_obs_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_perception_obs_pub_ + tcp_buffer_perception_obs_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_perception_obs_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    } else if (channel_name == "/apollo/canbus/chassis") {
      memcpy_s(tcp_buffer_chassis_pub_ + tcp_buffer_chassis_size_, sizeof(head), 
              &head, sizeof(head));
      memcpy_s(tcp_buffer_chassis_pub_ + tcp_buffer_chassis_size_ + sizeof(head),
              msg_size + msg_info_size, rb->buf, msg_size + msg_info_size);
      tcp_buffer_chassis_size_ += sizeof(TcpDataHead) + msg_size + msg_info_size;
    }
    #endif
    else {
      AERROR << "Unknown channel: " << channel_name;
      return;
    }
    seq_num = head.seq_num;  // for TRACE
  }
  if (FLAGS_trace_flag) {
    TRACE_BEGIN(trace::TCP_TRANSPORT, seq_num, trace::TcpTraceConfig);
  }
}

void TcpTransport::PubSingleMsg(char* publiser, std::atomic<int32_t>& tcp_buffer_size, bool& idle){
    while (tcp_buffer_size > 0) {
      idle = false;
      TcpDataHead *head = reinterpret_cast<TcpDataHead *>(publiser);
      auto rc = nn_send(pub_fd_, publiser, tcp_buffer_size, 0); 
      if (rc < 0) {
        AERROR << "nn_send failed, size =" << tcp_buffer_size
                << ",strerror=" << nn_strerror(nn_errno());
        pub_error_.store(true);
        break;
      }
      else{
        AINFO << "nn_send success, size =" << tcp_buffer_size;
      }
      // TODO(zheqiang.wu): log level
      AINFO << "nn_send success, size =" << tcp_buffer_size << ",rc=" << rc
            << ",seq_num:" << head->seq_num;
      tcp_buffer_size -= rc;

      if (tcp_buffer_size != 0) {
        AERROR << "~~~~~~nn_send faile, size =" << tcp_buffer_size
                << ",rc=" << rc;
      }
    }
}

/*
  * @brief 发布消息
  * 1. 检查发布错误标志，如果为 true，则重新创建发布者
  * 2. 循环发送缓冲区中的消息，直到缓冲区为空或发生错误
  * 3. 如果发送成功，更新缓冲区大小并记录日志
  * 4. 如果缓冲区为空，则休眠一段时间以减少 CPU 占用
*/
void TcpTransport::PubMsg() {
  bool idle = true;
  while (!shutdown_.load()) {
    if (pub_error_.load()) {
      std::this_thread::sleep_for(std::chrono::seconds(1));
      CreatePublisher();
      pub_error_.store(false);
    }
    idle = true;
    {
      LockGuard lock_guard(tcp_buffer_lock_);
      #if defined(__x86_64__)
      PubSingleMsg(tcp_buffer_plan_pub_, tcp_buffer_plan_size_, idle);
      PubSingleMsg(tcp_buffer_control_pub_, tcp_buffer_control_size_, idle);
      PubSingleMsg(tcp_buffer_monitor_pub_, tcp_buffer_monitor_size_, idle);
      PubSingleMsg(tcp_buffer_mcloud_pub_, tcp_buffer_mcloud_size_, idle);
      PubSingleMsg(tcp_buffer_mcloud_location_point_init_pub_,
                   tcp_buffer_mcloud_location_point_init_size_, idle);
      PubSingleMsg(tcp_buffer_mcloud_super_traffic_light_pub_,
                   tcp_buffer_mcloud_super_traffic_light_size_, idle);
      PubSingleMsg(tcp_buffer_mcloud_v2x_traffic_light_pub_,
                   tcp_buffer_mcloud_v2x_traffic_light_size_, idle);
      #elif defined(__aarch64__)
      PubSingleMsg(tcp_buffer_localization_pub_, tcp_buffer_localization_size_, idle);
      PubSingleMsg(tcp_buffer_prediction_pub_, tcp_buffer_prediction_size_, idle);
      PubSingleMsg(tcp_buffer_traffic_light_pub_, tcp_buffer_traffic_light_size_,idle);
      PubSingleMsg(tcp_buffer_perception_obs_pub_, tcp_buffer_perception_obs_size_, idle);
      PubSingleMsg(tcp_buffer_chassis_pub_, tcp_buffer_chassis_size_, idle);
      #endif
    }
    if (idle) {
      std::this_thread::sleep_for(std::chrono::microseconds(1000));
    }
  }
}

/*
  * @brief 订阅消息
  * 1. 循环接收消息，直到发生错误或关闭标志为 true
  * 2. 如果接收超时，则记录警告日志并继续循环
  * 3. 如果接收失败，则记录错误日志并设置订阅错误标志
  * 4. 如果接收成功，解析消息头并处理消息内容
*/
void TcpTransport::SubMsg() {
  while (!shutdown_.load()) {
    if (sub_error_.load()) {
      std::this_thread::sleep_for(std::chrono::seconds(1));
      CreateSubscriber();
      sub_error_.store(false);
    }
    auto rc = nn_recv(sub_fd_, tcp_buffer_sub_, kBufferSize, 0);
    if (rc < 0) {
      if (ETIMEDOUT == nn_errno()) {
        AWARN << "nn_recv timeout, rc =" << rc
              << ",strerror=" << nn_strerror(nn_errno());
        AWARN << "kBufferSize: " << kBufferSize;
      } else {
        AERROR << "nn_recv failed, rc =" << rc
               << ",strerror=" << nn_strerror(nn_errno());
        sub_error_.store(true);
      }
      continue;
    }
    // TODO(zheqiang.wu): log level
    AINFO << "nn_recv success,rc=" << rc;
    decltype(rc) pos = 0;
    while (pos < rc) {
      if (static_cast<uint32_t>(rc - pos) < sizeof(TcpDataHead)) {
        break;  // incomplete message header
      }
      TcpDataHead *head =
          reinterpret_cast<TcpDataHead *>(tcp_buffer_sub_ + pos);

      // TODO(zheqiang.wu): log level
      AINFO << "rc - pos=" << rc - pos << ",msg_size=" << head->msg_size
            << ",msg_info_size=" << head->msg_info_size
            << ",seq_num:" << head->seq_num
            << ",channel_name:" << apollo::cyber::common::GlobalData::GetChannelById(head->channel_id);
      if (static_cast<uint32_t>(rc - pos) <
          sizeof(TcpDataHead) + head->msg_size + head->msg_info_size) {
        break;  // incomplete message content
      }
#if 0
      MessageInfo msg_info;
      const char *msg_info_addr =
          tcp_buffer_sub_ + pos + sizeof(TcpDataHead) + head->msg_size;

      if (!msg_info.DeserializeFrom(msg_info_addr, head->msg_info_size)) {
        AERROR << "msg_info DeserializeFrom failed ";
      }
      auto msg = std::make_shared<Chatter>();
      RETURN_IF(!apollo::cyber::message::ParseFromArray(
          tcp_buffer_sub_ + pos + sizeof(TcpDataHead),
          static_cast<int>(head->msg_size), msg.get()));
      AERROR_EVERY(1) << msg->ShortDebugString();
#endif
      if (tcp_writer_.count(head->channel_id) > 0) {
        
        int ret = tcp_writer_[head->channel_id]->Write(
            tcp_buffer_sub_ + pos + sizeof(TcpDataHead), head->msg_size,
            head->msg_info_size);
        AINFO << "channel_id=" << head->channel_id
              << ",channel_name:" << apollo::cyber::common::GlobalData::GetChannelById(head->channel_id)
              << ",seq_num:" << head->seq_num 
              << ", ret: " << ret;
        if (FLAGS_trace_flag) {
          TRACE_END(trace::TCP_TRANSPORT, head->seq_num, trace::TcpTraceConfig);
        }
      }
      #if defined(__aarch64__)
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/planning"){
        if (plan_writer_) {
          AINFO <<"get /apollo/planning msg";
          std::shared_ptr<ADCTrajectory> plan;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, plan)){
            int ret = plan_writer_->Write(plan);
            // AINFO << "write channel_name=" << "/apollo/planning_tcp"
            //     << ",seq_num:" << head->seq_num 
            //     << ", ret1: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                    ", channel_name: "<< "/apollo/planning"<< 
                    ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id)
               == "/apollo/control"){
        if(control_writer_){
          AINFO <<"get /apollo/control msg";
          std::shared_ptr<ControlCommand> control;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, control)){
            int ret = control_writer_->Write(control);
            // AINFO << "write channel_name=" << "/apollo/control_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret2: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                    ", channel_name: "<< "/apollo/control"<< 
                    ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/monitor/monitor_data_x86"){
        if(monitor_writer_){
          AINFO <<"get /apollo/monitor/monitor_data_x86 msg";
          std::shared_ptr<MonitoredData> monitor;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, monitor)){
            int ret = monitor_writer_->Write(monitor);
            // AINFO << "write channel_name=" << "/apollo/monitor/monitor_data_x86_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret3: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                    ", channel_name: "<< "/apollo/monitor/monitor_data_x86"<< 
                    ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/mcloud"){
        if(mcloud_writer_){
          AINFO <<"get /apollo/mcloud msg";
          std::shared_ptr<McloudInfo> mcloud;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, mcloud)){
            int ret = mcloud_writer_->Write(mcloud);
            // AINFO << "write channel_name=" << "/apollo/mcloud_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret4: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                    ", channel_name: "<< "/apollo/mcloud"<< 
                    ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/mcloud/location_point_init"){
        if(mcloud_location_point_init_writer_){
          AINFO <<"get /apollo/mcloud/location_point_init msg";
          std::shared_ptr<LocationPointInit> mcloud_location_point_init;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, mcloud_location_point_init)){
            int ret = mcloud_location_point_init_writer_->Write(mcloud_location_point_init);
            // AINFO << "write channel_name=" << "/apollo/mcloud/location_point_init_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret5: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                    ", channel_name: "<< "/apollo/mcloud/location_point_init"<< 
                    ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/mcloud/super_traffic_light"){
        if(mcloud_super_traffic_light_writer_){
          AINFO <<"get /apollo/mcloud/super_traffic_light msg";
          std::shared_ptr<SuperTrafficLight> mcloud_super_traffic_light;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, mcloud_super_traffic_light)){
            int ret = mcloud_super_traffic_light_writer_->Write(mcloud_super_traffic_light);
            // AINFO << "write channel_name=" << "/apollo/mcloud/super_traffic_light_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret6: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                    ", channel_name: "<< "/apollo/mcloud/super_traffic_light"<< 
                    ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/mcloud/v2x_traffic_light"){
        if(mcloud_v2x_traffic_light_writer_){
          AINFO <<"get /apollo/mcloud/v2x_traffic_light msg";
          std::shared_ptr<V2XTrafficLightList> mcloud_v2x_traffic_light;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, mcloud_v2x_traffic_light)){
            int ret = mcloud_v2x_traffic_light_writer_->Write(mcloud_v2x_traffic_light);
            // AINFO << "write channel_name=" << "/apollo/mcloud/v2x_traffic_light_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret7: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                    ", channel_name: "<< "/apollo/mcloud/v2x_traffic_light"<< 
                    ", channel_msg: "<< head->msg_size;
          }
        }
      }
      #elif defined(__x86_64__)
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/localization/pose"){
        if(localization_pose_writer_){
          AINFO <<"get /apollo/localization/pose msg";
          std::shared_ptr<LocalizationEstimate> localization_pose;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, localization_pose)){
            int ret = localization_pose_writer_->Write(localization_pose);
            // AINFO << "write channel_name=" << "/apollo/localization/pose_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret7: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                      ", channel_name: "<< "/apollo/localization/pose"<< 
                      ", channel_msg: "<< head->msg_size;
          } 
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/prediction"){
        if(prediction_obs_writer_){
          AINFO <<"get /apollo/prediction msg";
          std::shared_ptr<PredictionObstacles> prediction_obs;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, prediction_obs)){
            int ret = prediction_obs_writer_->Write(prediction_obs);
            // AINFO << "write channel_name=" << "/apollo/prediction_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret7: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                      ", channel_name: "<< "/apollo/prediction"<< 
                      ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/perception/traffic_light"){
        if(traffic_light_writer_){
          AINFO <<"get /apollo/perception/traffic_light msg";
          std::shared_ptr<TrafficLightDetection> traffic_light_detection;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, traffic_light_detection)){
            int ret = traffic_light_writer_->Write(traffic_light_detection);
            // AINFO << "write channel_name=" << "/apollo/perception/traffic_light_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret7: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                      ", channel_name: "<< "/apollo/perception/traffic_light"<< 
                      ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/perception/obstacles"){
        if(perception_obs_writer_){
          AINFO <<"get /apollo/perception/obstacles msg";
          std::shared_ptr<PerceptionObstacles> perception_obs;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, perception_obs)){
            int ret = perception_obs_writer_->Write(perception_obs);
            // AINFO << "write channel_name=" << "/apollo/perception/obstacles_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret7: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                      ", channel_name: "<< "/apollo/perception/obstacles"<< 
                      ", channel_msg: "<< head->msg_size;
          }
        }
      }
      else if(apollo::cyber::common::GlobalData::GetChannelById(head->channel_id) 
              == "/apollo/canbus/chassis"){
        if(chassis_writer_){
          AINFO <<"get /apollo/canbus/chassis msg";
          std::shared_ptr<Chassis> chassis;
          if(ParseTcpBufferToProto(tcp_buffer_sub_, *head, chassis)){
            int ret = chassis_writer_->Write(chassis);
            // AINFO << "write channel_name=" << "/apollo/canbus/chassis_tcp"
            //       << ",seq_num:" << head->seq_num 
            //       << ", ret7: " << ret;
          }
          else{
            AINFO <<"ParseTcpBufferToProto failed"<<
                      ", channel_name: "<< "/apollo/canbus/chassis"<< 
                      ", channel_msg: "<< head->msg_size;
          }
        }
      }
      #endif
      else {
        AERROR << "not support channel_id:" << head->channel_id;
      }
      pos += sizeof(TcpDataHead) + head->msg_size + head->msg_info_size;
    }
  }
}

template<typename ProtoType>
bool TcpTransport::ParseTcpBufferToProto(const char* buffer, const TcpDataHead& head, 
                             std::shared_ptr<ProtoType>& proto_msg){
  const char* msg_data = buffer + sizeof(TcpDataHead);
  proto_msg = std::make_shared<ProtoType>();
  return apollo::cyber::message::ParseFromArray(
    msg_data, static_cast<int>(head.msg_size), proto_msg.get()  
  );
}

}  // namespace tcp_transport
}  // namespace apollo
