load("//tools:apollo_package.bzl", "apollo_cc_library", "apollo_cc_binary", "apollo_cc_test",  "apollo_package")
load("//tools/install:install.bzl", "install")

load("//tools:cpplint.bzl", "cpplint")

package(default_visibility=["//visibility:public"])

TCP_TRANSPORT_COPTS = ['-DMODULE_NAME=\\"tcp_transport\\"']

filegroup(
    name="runtime_data",
    srcs=glob(
        [
            "conf/*.txt",
            "dag/*.dag",
            "launch/*.launch",
        ]
    ),
)

install(
    name="install",
    data=[
        ":runtime_data",
    ],
    targets=[
        ":libtcp_transport_component.so",
    ],
    deps=[
        "//cyber:install",
    ],
)

apollo_cc_library(
    name="tcp_transport_lib",
    copts=TCP_TRANSPORT_COPTS,
    srcs=["tcp_transport.cc"],
    hdrs=["tcp_transport.h"],
    linkopts=["-pthread"],
    deps=[
        "//cyber",
        "//modules/tcp_transport/common:tt_gflags",
        "//modules/common_msgs/planning_msgs:planning_cc_proto",
        "//modules/common_msgs/control_msgs:control_cmd_cc_proto",
        "//modules/common_msgs/monitor_msgs:system_status_cc_proto",
        "//modules/common_msgs/mcloud_msgs:mcloud_info_cc_proto",
        "//modules/common_msgs/mcloud_msgs:location_point_init_cc_proto",
        "//modules/common_msgs/mcloud_msgs:super_traffic_light_cc_proto",
        "//modules/common_msgs/mcloud_msgs:v2x_traffic_light_cc_proto",
        "//modules/common_msgs/localization_msgs:localization_cc_proto",
        "//modules/common_msgs/prediction_msgs:prediction_obstacle_cc_proto",
        "//modules/common_msgs/perception_msgs:traffic_light_detection_cc_proto",
        "//third_party/nanomsg:nanomsg",
        "//third_party/safec:safec",
    ],
)
apollo_cc_library(
    name="tcp_transport_component_lib",
    copts=TCP_TRANSPORT_COPTS,
    srcs=["tcp_transport_component.cc"],
    hdrs=["tcp_transport_component.h"],
    linkopts = select(
        {
            "@platforms//cpu:aarch64": [
                "-L/usr/lib/aarch64-linux-gnu/tegra/",
                "-lnvbufsurface",
                "-lnvbufsurftransform",
                "-L/usr/lib/aarch64-linux-gnu/",
                "-lv4l2",
            ],
            "@platforms//cpu:x86_64": [],
        },
    ),    
    deps=[
        ":tcp_transport_lib",
    ],
)

apollo_cc_binary(
    name="libtcp_transport_component.so",
    linkshared=True,
    linkstatic=False,
    deps=[":tcp_transport_component_lib"],
)

apollo_package()

cpplint()
