topic_conf {
  adccontainer_topic_name: "/apollo/prediction/adccontainer"
  container_topic_name: "/apollo/prediction/container"
  evaluator_topic_name: "/apollo/prediction/evaluator"
  localization_topic: "/apollo/localization/pose"
  perception_obstacle_topic: "/apollo/perception/obstacles"
  perception_obstacles_topic_name: "/apollo/prediction/perception_obstacles"
  planning_trajectory_topic: "/apollo/planning_tcp"
  prediction_topic: "/apollo/prediction"
  storytelling_topic: "/apollo/storytelling"
}
evaluator_model_conf {
  model {
    evaluator_type: SEMANTIC_LSTM_EVALUATOR
    obstacle_type: PEDESTRIAN
    backend: GPU
    priority: 10
    type: "SemanticLstmPedestrianGpuTorch"
  }
  model {
    evaluator_type: SEMANTIC_LSTM_EVALUATOR
    obstacle_type: VEHICLE
    backend: GPU
    priority: 10
    type: "SemanticLstmVehicleGpuTorch"
  }
  model {
    evaluator_type: SEMANTIC_LSTM_EVALUATOR
    obstacle_type: PEDESTRIAN
    backend: CPU
    priority: 10
    type: "SemanticLstmPedestrianCpuTorch"
  }
  model {
    evaluator_type: SEMANTIC_LSTM_EVALUATOR
    obstacle_type: VEHICLE
    backend: CPU
    priority: 10
    type: "SemanticLstmVehicleCpuTorch"
  }
  model {
    evaluator_type: MULTI_AGENT_EVALUATOR
    obstacle_type: PEDESTRIAN
    backend: GPU
    priority: 10
    type: "MultiAgentPedestrianGpuTorch"
  }
  model {
    evaluator_type: MULTI_AGENT_EVALUATOR
    obstacle_type: PEDESTRIAN
    backend: CPU
    priority: 10
    type: "MultiAgentPedestrianCpuTorch"
  }
  model {
    evaluator_type: MULTI_AGENT_EVALUATOR
    obstacle_type: VEHICLE
    backend: GPU
    priority: 10
    type: "MultiAgentVehicleGpuTorch"
  }
  model {
    evaluator_type: MULTI_AGENT_EVALUATOR
    obstacle_type: VEHICLE
    backend: CPU
    priority: 10
    type: "MultiAgentVehicleCpuTorch"
  }
}
obstacle_conf {
  obstacle_type: VEHICLE
  obstacle_status: ON_LANE
  priority_type: CAUTION
  evaluator_type: VECTORNET_EVALUATOR
  predictor_type: EXTRAPOLATION_PREDICTOR
}
obstacle_conf {
  obstacle_type: VEHICLE
  obstacle_status: ON_LANE
  priority_type: NORMAL
  evaluator_type: CRUISE_MLP_EVALUATOR
  predictor_type: MOVE_SEQUENCE_PREDICTOR
}
obstacle_conf {
  obstacle_type: VEHICLE
  obstacle_status: IN_JUNCTION
  priority_type: CAUTION
  evaluator_type: VECTORNET_EVALUATOR
  predictor_type: EXTRAPOLATION_PREDICTOR
}
obstacle_conf {
  obstacle_type: VEHICLE
  obstacle_status: IN_JUNCTION
  priority_type: NORMAL
  evaluator_type: JUNCTION_RULE_EVALUATOR
  predictor_type: MOVE_SEQUENCE_PREDICTOR
}
obstacle_conf {
  obstacle_type: VEHICLE
  obstacle_status: OFF_LANE
  evaluator_type: VEHICLE_OFFLANE_EVALUATOR
  predictor_type: MOVE_SEQUENCE_PREDICTOR
}
obstacle_conf {
  obstacle_type: PEDESTRIAN
  obstacle_status: MOVING
  evaluator_type: SEMANTIC_LSTM_EVALUATOR
  predictor_type: FREE_MOVE_PREDICTOR
}
obstacle_conf {
  obstacle_type: BICYCLE
  obstacle_status: ON_LANE
  evaluator_type: CYCLIST_KEEP_LANE_EVALUATOR
  predictor_type: LANE_SEQUENCE_PREDICTOR
}
obstacle_conf {
  obstacle_type: BICYCLE
  obstacle_status: OFF_LANE
  predictor_type: FREE_MOVE_PREDICTOR
}
obstacle_conf {
  obstacle_type: UNKNOWN
  obstacle_status: ON_LANE
  evaluator_type: CYCLIST_KEEP_LANE_EVALUATOR
  predictor_type: LANE_SEQUENCE_PREDICTOR
}
obstacle_conf {
  obstacle_type: UNKNOWN
  obstacle_status: OFF_LANE
  predictor_type: FREE_MOVE_PREDICTOR
}
obstacle_conf {
  obstacle_type: VEHICLE
  interactive_tag: INTERACTION
  evaluator_type: VERDEX_VECTORNET_EVALUATOR
  predictor_type: EXTRAPOLATION_PREDICTOR
}
