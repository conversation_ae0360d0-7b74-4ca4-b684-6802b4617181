load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]

apollo_cc_library(
    name = "teb_tar_fsm_common",
    hdrs = ["teb_tar_fsm_common.h"],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/common_msgs/planning_msgs:planning_proto",
        "//cyber/time:cyber_time",
        "//cyber/timer:cyber_timer",
    ],
)

apollo_cc_library(
    name = "tar_condition",
    hdrs = ["tar_condition.h"],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/planning/common:frame",
        "//modules/planning/tasks/deciders/teb_planner_decider/teb_pre_observation_decider:teb_tar_fsm_common",
    ],
)

apollo_cc_library(
    name = "tar_action",
    hdrs = ["tar_action.h"],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/planning/common:frame",
    ],
)

apollo_cc_library(
    name = "teb_tar_decider_fsm",
    srcs = ["teb_tar_decider_fsm.cc"],
    hdrs = ["teb_tar_decider_fsm.h"],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/planning/tasks/deciders/teb_planner_decider/teb_pre_observation_decider:tar_condition",
        "//modules/planning/tasks/deciders/teb_planner_decider/teb_pre_observation_decider:tar_action",
        "//modules/planning/common:planning_context",
        "//modules/planning/common:planning_gflags",
        "//modules/planning/common:frame",
        "//modules/planning/tasks/deciders/teb_planner_decider/teb_pre_observation_decider:teb_tar_fsm_common",
        "//modules/planning/common:dependency_injector",

    ],
    
)

apollo_cc_library(
    name = "teb_pre_observation_decider",
    srcs = ["teb_pre_observation_decider.cc"],
    hdrs = ["teb_pre_observation_decider.h"],
    copts = PLANNING_COPTS,
        linkopts = [
            "-lm",
            "-lgomp",
        ],
    deps = [
        "//modules/dreamview/backend/map:map_service",
        "//modules/planning/common:planning_context",
        "//modules/planning/common:planning_gflags",
        "//modules/planning/open_space/teb:teb_planner",
        "//modules/planning/open_space/teb:teb_timed_elastic_band",
        "//modules/planning/open_space/teb:teb_visualization",
        "//modules/planning/tasks/deciders:decider_base",
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "//modules/planning/open_space/teb/g2o_types",
        "//modules/planning/open_space/teb/utils:g2o_teb_types",
        "//modules/planning/tasks/deciders/teb_planner_decider/teb_pre_observation_decider:teb_tar_decider_fsm"
    ],
    
)

apollo_package()
cpplint()
