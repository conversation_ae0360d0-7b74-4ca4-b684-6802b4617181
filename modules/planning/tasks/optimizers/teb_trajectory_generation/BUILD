load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")
#load("@local_config_cuda//cuda:build_defs.bzl", "cuda_library")
load("//tools/platform:build_defs.bzl", "if_gpu")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]
FOPENMP_COPTS = ["-fopenmp"]

apollo_cc_library(
    name = "teb_trajectory_provider",
    srcs = ["teb_trajectory_provider.cc"],
    hdrs = ["teb_trajectory_provider.h"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
        "-fopenmp",
    ],
    deps = [
        ":teb_trajectory_optimizer",
        "//modules/common/status",
        "//modules/planning/common:planning_common",
        "//modules/planning/common:planning_gflags",
        "//modules/planning/common:trajectory_stitcher",
        "//modules/planning/common/trajectory:discretized_trajectory",
        "//modules/planning/tasks:task",
        "//modules/planning/tasks/optimizers:trajectory_optimizer",
       ] 
       #+ if_gpu(["@local_config_cuda//cuda:cudart"]),
)

apollo_cc_library(
    name = "teb_trajectory_optimizer",
    srcs = ["teb_trajectory_optimizer.cc"],
    hdrs = ["teb_trajectory_optimizer.h"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
        "-fopenmp",
    ],
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
        "//modules/common/status",
        "//modules/common/util:util_lib",
        "//modules/common/vehicle_state:vehicle_state_provider",
        "//modules/planning/common:frame",
        "//modules/planning/open_space/coarse_trajectory_generator:hybrid_a_star",
        "//modules/planning/open_space/trajectory_smoother:distance_approach_problem",
        "//modules/planning/open_space/trajectory_smoother:dual_variable_warm_start_problem",
        "//modules/planning/open_space/trajectory_smoother:iterative_anchoring_smoother",
        "//modules/common_msgs/planning_msgs:planning_config_proto",
        "//modules/planning/open_space/teb:teb_planner",
        "//modules/planning/open_space/teb:teb_timed_elastic_band",
        "//modules/planning/open_space/teb:teb_visualization",
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "//modules/planning/open_space/teb/g2o_types",
        "//modules/planning/open_space/teb/utils:g2o_teb_types",
        "@com_github_gflags_gflags//:gflags",
        "@eigen",
    ],
)

apollo_package()
cpplint()
