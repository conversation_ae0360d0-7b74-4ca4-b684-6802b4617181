load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")

package(default_visibility = ["//visibility:public"])

licenses(["notice"])

apollo_cc_library(
    name = "g2o_header",
    hdrs = glob([
        "include/g2o/autodiff/*.h",
        "include/g2o/core/*.hpp",
        "include/g2o/core/*.h",
        "include/g2o/solvers/cholmod/*.h",
        "include/g2o/solvers/csparse/*.h",
        "include/g2o/solvers/dense/*.h",
        "include/g2o/solvers/eigen/*.h",
        "include/g2o/solvers/pcg/*.h",
        "include/g2o/solvers/pcg/*.hpp",
        "include/g2o/solvers/slam2d_linear/*.h",
        "include/g2o/solvers/structure_only/*.h",
        "include/g2o/stuff/*.h",
        "include/g2o/types/data/*.h",
        "include/g2o/types/icp/*.h",
        "include/g2o/types/sba/*.h",
        "include/g2o/types/sclam2d/*.h",
        "include/g2o/types/sim3/*.h",
        "include/g2o/types/slam2d/*.h",
        "include/g2o/types/slam2d_addons/*.h",
        "include/g2o/types/slam3d/*.h",
        "include/g2o/types/slam3d_addons/*.h",
        "include/g2o/config.h",
    ]),
    linkopts = [
        "-lm",
    ],
    copts = [
        "-fopenmp", 
    ],
    strip_include_prefix = select({
        "@platforms//cpu:x86_64": "include",
    }),
)

apollo_cc_library(
    name = "g2o_teb",
    srcs = glob([
        "build/lib/*.so*",
    ]),
    hdrs = glob([
        "include/g2o/*/*.h",
        "include/g2o/*.h",
    ]),
    include_prefix = "g2o",
    linkopts = [
        "-lm",
    ],
    copts = [
        "-fopenmp", 
    ],
    strip_include_prefix = select({
        "@platforms//cpu:x86_64": "include",
    }),
)

apollo_package()
cpplint()
