load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")
#load("@local_config_cuda//cuda:build_defs.bzl", "cuda_library")
load("//tools/platform:build_defs.bzl", "if_gpu")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]


apollo_cc_library(
    name = "dual_variable_warm_start_problem",
    srcs = ["dual_variable_warm_start_problem.cc"],
    hdrs = ["dual_variable_warm_start_problem.h"],
    copts = PLANNING_COPTS,
    deps = [
        ":dual_variable_warm_start_ipopt_interface",
        ":dual_variable_warm_start_ipopt_qp_interface",
        ":dual_variable_warm_start_osqp_interface",
        ":dual_variable_warm_start_slack_osqp_interface",
        "//cyber/common:cyber_common",
        "//modules/common/util:util_lib",
    ],
)

apollo_cc_library(
    name = "dual_variable_warm_start_ipopt_interface",
    srcs = ["dual_variable_warm_start_ipopt_interface.cc"],
    hdrs = ["dual_variable_warm_start_ipopt_interface.h"],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ],
)

apollo_cc_library(
    name = "dual_variable_warm_start_ipopt_qp_interface",
    srcs = ["dual_variable_warm_start_ipopt_qp_interface.cc"],
    hdrs = ["dual_variable_warm_start_ipopt_qp_interface.h"],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ],
)

apollo_cc_library(
    name = "dual_variable_warm_start_osqp_interface",
    srcs = ["dual_variable_warm_start_osqp_interface.cc"],
    hdrs = ["dual_variable_warm_start_osqp_interface.h"],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@eigen",
        "@osqp",
    ],
)

apollo_cc_library(
    name = "dual_variable_warm_start_slack_osqp_interface",
    srcs = ["dual_variable_warm_start_slack_osqp_interface.cc"],
    hdrs = ["dual_variable_warm_start_slack_osqp_interface.h"],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@eigen",
        "@osqp",
    ],
)

apollo_cc_library(
    name = "distance_approach_problem",
    srcs = ["distance_approach_problem.cc"],
    hdrs = ["distance_approach_problem.h"],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        ":distance_approach_ipopt_cuda_interface",
        ":distance_approach_ipopt_fixed_dual_interface",
        ":distance_approach_ipopt_fixed_ts_interface",
        ":distance_approach_ipopt_interface",
        ":distance_approach_ipopt_relax_end_interface",
        ":distance_approach_ipopt_relax_end_slack_interface",
        "//cyber/common:cyber_common",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ],
)

apollo_cc_library(
    name = "distance_approach_ipopt_fixed_ts_interface",
    srcs = ["distance_approach_ipopt_fixed_ts_interface.cc"],
    hdrs = [
        "distance_approach_interface.h",
        "distance_approach_ipopt_fixed_ts_interface.h",
    ],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ],
)

apollo_cc_library(
    name = "distance_approach_ipopt_fixed_dual_interface",
    srcs = ["distance_approach_ipopt_fixed_dual_interface.cc"],
    hdrs = [
        "distance_approach_interface.h",
        "distance_approach_ipopt_fixed_dual_interface.h",
    ],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ],
)

apollo_cc_library(
    name = "distance_approach_ipopt_relax_end_interface",
    srcs = ["distance_approach_ipopt_relax_end_interface.cc"],
    hdrs = [
        "distance_approach_interface.h",
        "distance_approach_ipopt_relax_end_interface.h",
    ],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ],
)

apollo_cc_library(
    name = "distance_approach_ipopt_relax_end_slack_interface",
    srcs = ["distance_approach_ipopt_relax_end_slack_interface.cc"],
    hdrs = [
        "distance_approach_interface.h",
        "distance_approach_ipopt_relax_end_slack_interface.h",
    ],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ],
)

apollo_cc_library(
    name = "distance_approach_ipopt_interface",
    srcs = ["distance_approach_ipopt_interface.cc"],
    hdrs = [
        "distance_approach_interface.h",
        "distance_approach_ipopt_interface.h",
    ],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ],
)

apollo_cc_library(
    name = "distance_approach_ipopt_cuda_interface",
    srcs = ["distance_approach_ipopt_cuda_interface.cc"],
    hdrs = [
        "distance_approach_interface.h",
        "distance_approach_ipopt_cuda_interface.h",
    ],
    copts = [
        "-fopenmp",
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    linkopts = ["-lgomp"],
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/common/util:util_lib",
        "//modules/planning/common:planning_gflags",
        "//modules/common_msgs/planning_msgs:planner_open_space_config_proto",
        "//modules/common_msgs/planning_msgs:planning_proto",
        "@adolc",
        "@eigen",
        "@ipopt",
    ] 
    #+ if_gpu([":planning_block"]),
)

apollo_cc_library(
    name = "iterative_anchoring_smoother",
    srcs = ["iterative_anchoring_smoother.cc"],
    hdrs = ["iterative_anchoring_smoother.h"],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/common/configs:vehicle_config_helper",
        "//modules/common/math",
        "//modules/planning/common:speed_profile_generator",
        "//modules/planning/common/path:discretized_path",
        "//modules/planning/common/speed:speed_data",
        "//modules/planning/common/trajectory:discretized_trajectory",
        "//modules/planning/math:discrete_points_math",
        "//modules/planning/math/discretized_points_smoothing:fem_pos_deviation_smoother",
        "@eigen",
    ],
)

#cuda_library(
#    name = "planning_block",
#    srcs = ["planning_block.cu"],
#    hdrs = ["planning_block.h"],
#    copts = PLANNING_COPTS,
#    deps = [
#        "@local_config_cuda//cuda:cuda_headers",
#        "@local_config_cuda//cuda:cudart",
#    ],
#)

apollo_package()
cpplint()