load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]

apollo_cc_library(
    name = "g2o_misc",
    hdrs = [
        "misc.h",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)

apollo_cc_library(
    name = "g2o_distance_calculations",
    hdrs = [
        "distance_calculations.h",
    ],
    copts = PLANNING_COPTS,
    deps = [
        ":g2o_misc",
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)

apollo_cc_library(
    name = "g2o_teb_types",
    hdrs = [
        "teb_types.h",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)

apollo_cc_library(
    name = "g2o_pose_se2",
    hdrs = [
        "pose_se2.h",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)

apollo_cc_library(
    name = "g2o_utils",
    srcs = ["teb_config.cc"],
    hdrs = [
        "distance_calculations.h",
        "equivalence_relations.h",
        "misc.h",
        "pose_se2.h",
        "robot_footprint_model.h",
        "teb_config.h",
        "teb_types.h",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//cyber/common:cyber_common",
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)
apollo_package()
cpplint()