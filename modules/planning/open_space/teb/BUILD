load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]

apollo_cc_library(
    name = "teb_planner",
    srcs = ["optimal_planner.cc"],
    hdrs =
        [
            "optimal_planner.h",
            "planner_interface.h",
        ],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
        "-fopenmp",
    ],
    includes = [
        ".",
    ],
    deps = [
        "//cyber",
        "//cyber/common:cyber_common",
        "//modules/planning/open_space/teb/g2o_types",
        "//modules/planning/open_space/teb/utils:g2o_teb_types",
        "//modules/planning/open_space/teb/utils:g2o_utils",
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)

apollo_cc_library(
    name = "teb_timed_elastic_band",
    srcs = ["timed_elastic_band.cc"],
    hdrs =
        ["timed_elastic_band.h"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
        "-fopenmp",
    ],
    includes = [
        ".",
    ],
    deps = [
        "//cyber",
        "//cyber/common:cyber_common",
        "//modules/planning/open_space/teb/g2o_types",
        "//modules/planning/open_space/teb/utils:g2o_teb_types",
        "//modules/planning/open_space/teb/utils:g2o_utils",
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)

apollo_cc_library(
    name = "teb_visualization",
    srcs = ["visualization.cc"],
    hdrs =
        ["visualization.h"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
        "-fopenmp",
    ],
    includes = [
        ".",
    ],
    deps = [
        "//cyber",
        "//cyber/common:cyber_common",
        "//modules/planning/open_space/teb/g2o_types",
        "//modules/planning/open_space/teb/utils:g2o_teb_types",
        "//modules/planning/open_space/teb/utils:g2o_utils",
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)

apollo_package()
cpplint()
