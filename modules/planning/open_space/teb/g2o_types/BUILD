load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]

apollo_cc_library(
    name = "g2o_types",
    srcs = ["teb_obstacles.cc"],
    hdrs = [
        "base_teb_edges.h",
        "edge_acceleration.h",
        "edge_dynamic_obstacle.h",
        "edge_kinematics.h",
        "edge_obstacle.h",
        "edge_prefer_rotdir.h",
        "edge_shortest_path.h",
        "edge_time_optimal.h",
        "edge_velocity.h",
        "edge_via_point.h",
        "penalties.h",
        "teb_obstacles.h",
        "vertex_pose.h",
        "vertex_timediff.h",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//cyber/common:cyber_common",
        "//modules/planning/open_space/teb/utils:g2o_distance_calculations",
        "//modules/planning/open_space/teb/utils:g2o_teb_types",
        "//modules/planning/teb:g2o_header",
        "//modules/planning/teb:g2o_teb",
        "@boost",
        "@eigen",
    ],
)

apollo_package()
cpplint()

