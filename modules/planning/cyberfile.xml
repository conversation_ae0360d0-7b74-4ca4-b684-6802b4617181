<package format="2">
  <name>planning</name>
  <version>local</version>
  <description>
    Compared with previous versions, Apollo 7.0 adds a  new dead end scenario, adds the "three-point turn",  increases vehicle driving in and out ability, and expands the operation boundary of the urban road network. The "three-point turn" function is based on the open space planner framework and includes the following parts: scene conversion of dead ends, construction of open space ROI, and "three-point turn" trajectory planning.
  </description>
  
  <maintainer email="<EMAIL>">Apollo</maintainer>
  <license>Apache License 2.0</license>
  <url type="website">https://www.apollo.auto/</url>
  <url type="repository">https://github.com/ApolloAuto/apollo</url>
  <url type="bugtracker">https://github.com/ApolloAuto/apollo/issues</url>

  <type>module</type>
  <src_path url="https://github.com/ApolloAuto/apollo">//modules/planning</src_path>

  <depend repo_name="com_github_gflags_gflags" lib_names="gflags">3rd-gflags</depend>
  <depend repo_name="com_google_absl" lib_names="absl">3rd-absl</depend>
  <depend repo_name="osqp">3rd-osqp</depend>
  <depend repo_name="com_github_google_glog" lib_names="glog">3rd-glog</depend>
  <depend repo_name="proj">3rd-proj</depend>
  <depend repo_name="tinyxml2" so_names="tinyxml2">libtinyxml2-dev</depend>
  <depend repo_name="boost">3rd-boost</depend>
  <depend repo_name="opencv" lib_names="core,highgui,imgproc,imgcodecs">3rd-opencv</depend>
  <depend repo_name="ipopt">3rd-ipopt</depend>
  <depend repo_name="eigen">3rd-eigen3</depend>
  <depend repo_name="civetweb">3rd-civetweb</depend>
  <depend repo_name="adolc" so_names="adolc">libadolc-dev</depend>
  <depend repo_name="ad_rss_lib" lib_names="ad_rss">3rd-ad-rss-lib</depend>
  <depend type="binary" repo_name="cyber">cyber</depend>
  <depend type="binary" repo_name="common" lib_names="common">common</depend>
  <depend type="binary" repo_name="map" lib_names="map">map</depend>
  <depend type="binary" repo_name="common-msgs" lib_names="common-msgs">common-msgs</depend>
  <!-- <depend type="binary" repo_name="prediction">prediction</depend> -->
  <depend>bazel-extend-tools</depend>
  <depend>3rd-mkl</depend>
  <depend repo_name="libtorch_cpu" lib_names="libtorch_cpu">3rd-libtorch-cpu</depend>
  <depend lib_names="protobuf" repo_name="com_google_protobuf">3rd-protobuf</depend>

  <depend expose="False">3rd-rules-python</depend>
  <depend expose="False">3rd-grpc</depend>
  <depend expose="False">3rd-bazel-skylib</depend>
  <depend expose="False">3rd-rules-proto</depend>
  <depend expose="False">3rd-py</depend>
  <depend expose="False">3rd-pcl</depend>
  <!-- <depend expose="False">3rd-gpus</depend> -->

  <depend repo_name="com_google_googletest" lib_names="gtest,gtest_main">3rd-gtest</depend>
  
  <!-- external_command -->
  <depend repo_name="external-command-action" type="binary">external-command-action</depend>
  <depend repo_name="external-command-demo" type="binary">external-command-demo</depend>
  <depend repo_name="external-command-lane-follow" type="binary">external-command-lane-follow</depend>
  <depend repo_name="external-command-process" type="binary">external-command-process</depend>
  <depend repo_name="external-command-processor-base" type="binary">external-command-processor-base</depend>
  <depend repo_name="external-command-valet-parking" type="binary">external-command-valet-parking</depend>
  <depend repo_name="old-routing-adpter" type="binary">old-routing-adpter</depend>
  <depend repo_name="routing" type="binary">routing</depend>
</package>
