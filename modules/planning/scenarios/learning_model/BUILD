load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_cc_test", "apollo_component", "apollo_package")
load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")
# load("@local_config_cuda//cuda:build_defs.bzl", "cuda_library")
# load("//tools/platform:build_defs.bzl", "if_gpu")
# load("//third_party/gpus:common.bzl", "gpu_library", "if_cuda", "if_rocm")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]


apollo_cc_library(
    name = "learning_model",
    copts = PLANNING_COPTS,
    deps = [
        ":learning_model_sample_scenario",
    ],
)

apollo_cc_library(
    name = "learning_model_sample_scenario",
    srcs = [
        "learning_model_sample_scenario.cc",
        "stage_run.cc",
    ],
    hdrs = [
        "learning_model_sample_scenario.h",
        "stage_run.h",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//cyber/common:cyber_common",
        "//modules/common_msgs/basic_msgs:pnc_point_proto",
        "//modules/common/status",
        "//modules/common/util:util_lib",
        "//modules/planning/reference_line",
        "//modules/planning/scenarios:scenario",
        "@eigen",
    ] 
    # + if_gpu(
    #     ["@libtorch_gpu"],
    #     ["@libtorch_cpu"],
    # ),
)

apollo_package()
cpplint()
