module_config {
  module_library : "modules/perception/radar_detection/libradar_detection.so"
  components {
    class_name: "RadarPerceptionComponent"
    config {
      name: "RadarPerception"
      config_file_path: "modules/perception/radar_detection/conf/radar_component_conf.pb.txt"
      flag_file_path: "modules/perception/data/flag/perception_common.flag"
      readers {
        channel: "/apollo/sensor/radar"
      }
    }
  }

}
