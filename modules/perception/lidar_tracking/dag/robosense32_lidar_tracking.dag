module_config {
  module_library : "modules/perception/lidar_tracking/liblidar_tracking_component.so"
  components {
    class_name : "LidarTrackingComponent"
    config {
      name : "LidarTrackingRs"
      config_file_path : "modules/perception/lidar_tracking/conf/robosense32_lidar_tracking_config.pb.txt"
      flag_file_path: "modules/perception/data/flag/perception_common.flag"
      readers {
        channel: "/perception/rs/detection"
      }
    }
  }
}
