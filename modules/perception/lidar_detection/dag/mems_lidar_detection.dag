module_config {
  module_library : "modules/perception/lidar_detection/liblidar_detection_component.so"
  components {
    class_name : "LidarDetectionComponent"
    config {
      name : "LidarDetectionMems"
      config_file_path : "modules/perception/lidar_detection/conf/mems_lidar_detection_config.pb.txt"
      flag_file_path: "modules/perception/data/flag/perception_common.flag"
      readers {
        channel: "/perception/mems/pointcloud_preprocess"
      }
    }
  }
}
