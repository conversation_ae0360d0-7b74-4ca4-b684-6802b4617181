module_config {
  module_library : "modules/perception/pointcloud_preprocess/libpointcloud_preprocess_component.so"

  components {
    class_name : "PointCloudPreprocessComponent"
    config {
      name : "PointCloudPreprocessMems"
      config_file_path : "modules/perception/pointcloud_preprocess/conf/mems_pointcloud_preprocess_config.pb.txt"
      flag_file_path: "modules/perception/data/flag/perception_common.flag"
    }
  }
}
