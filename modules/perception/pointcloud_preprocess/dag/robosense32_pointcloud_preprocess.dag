module_config {
  module_library : "modules/perception/pointcloud_preprocess/libpointcloud_preprocess_component.so"

  timer_components {
    class_name : "TimerComponent"
    config {
      name: "TimeDetection"
      interval: 100
    }
  }


  components {
    class_name : "PointCloudPreprocessComponent"
    config {
      name : "PointCloudPreprocessRs"
      config_file_path : "modules/perception/pointcloud_preprocess/conf/robosense32_pointcloud_preprocess_config.pb.txt"
      flag_file_path: "modules/perception/data/flag/perception_common.flag"
    }
  }
}
