sensor_name: "robosense32"
lidar_query_tf_offset: 0
output_channel_name: "/perception/rs/pointcloud_preprocess"
lidar2novatel_tf2_child_frame_id: "rslidar_32"
plugin_param {
  name: "PointCloudPreprocessor"
  config_path: "perception/pointcloud_preprocess/data"
  config_file: "robosense32_pointcloud_preprocessor.pb.txt"
}
trigger_by_data_input: true
input_channel_name: "/apollo/sensor/rshelios/PointCloud2"
use_inner_message: false