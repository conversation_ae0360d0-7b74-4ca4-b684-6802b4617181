<cyber>
    <desc>Apollo lidar 3d object detection</desc>
    <version>1.0.0</version>
    <module>
        <name>lidar</name>
        <dag_conf>modules/drivers/lidar/zvision/dag/fusion.dag</dag_conf>
        <!-- <dag_conf>modules/perception/pointcloud_preprocess/dag/robosense32_pointcloud_preprocess.dag</dag_conf> -->
        <dag_conf>modules/perception/pointcloud_preprocess/dag/mems_pointcloud_preprocess.dag</dag_conf>
        <!-- <dag_conf>modules/perception/lidar_detection/dag/robosense32_lidar_detection.dag</dag_conf> -->
        <!-- <dag_conf>modules/perception/lidar_detection/dag/mems_lidar_detection.dag</dag_conf> -->
        <dag_conf>modules/transform/dag/static_transform.dag</dag_conf>
        <!-- <dag_conf>modules/perception/lidar_tracking/dag/robosense32_lidar_tracking.dag</dag_conf> -->
        <!-- <dag_conf>modules/perception/lidar_tracking/dag/mems_lidar_tracking.dag</dag_conf> -->
        <!-- <dag_conf>modules/websocket_single/dag/websocket.dag</dag_conf> -->
        <!-- <dag_conf>modules/perception/radar_detection/dag/radar_detection.dag</dag_conf> -->
        <!-- <dag_conf>modules/perception/ultrasonic_detection/dag/ultrasonic_radar.dag</dag_conf> -->
        <!-- <dag_conf>modules/perception/msg_adapter/dag/msg_adapter.dag</dag_conf> -->
        <!-- <dag_conf>modules/perception/multi_sensor_fusion/dag/multi_sensor_fusion.dag</dag_conf> -->
        <process_name>lidar</process_name>
        <version>1.0.0</version>
    </module>
</cyber>