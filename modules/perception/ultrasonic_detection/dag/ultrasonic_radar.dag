module_config {
  module_library : "modules/perception/ultrasonic_detection/libultrasonic_radar_detection_component.so"
  components {
    class_name : "UltrasonicRadarComponent"
    config {
      name : "UltrasonicPerception"
      config_file_path : "modules/perception/ultrasonic_detection/conf/ultrasonic_radar_detection_component.config"
      flag_file_path: "modules/perception/data/flag/perception_common.flag"
      readers {
        channel: "/apollo/sensor/udas_ultrasonic"
        pending_queue_size: 10
      }
    }
  }
}
