topic_config {
  chassis_topic: "/apollo/canbus/chassis"
  localization_topic: "/apollo/localization/pose_tcp"
  mcloud_topic: "/apollo/mcloud"
  robosense_vehicle_state_topic: "/robosense/vehicle_state"
  monitor_data_topic: "/apollo/monitor/monitor_data_x86"
  routing_request_topic: "/apollo/routing_request"
  planning_trajectory_topic: "/apollo/planning"
  super_traffic_light_topic: "/apollo/mcloud/super_traffic_light"
  perception_obstacles_topic: "/apollo/perception/obstacles"
  routing_result_topic: "/apollo/routing_result"
  traffic_light_states_topic: "/apollo/planning/traffic_light_states"
  udas_ultrasonic_eol_topic: "/apollo/sensor/udas_ultrasonic_eol"
  cubtek_radar_eol_topic: "/apollo/sensor/radar_eol"
  front_12mm_status_topic: "/apollo/camera/front_12mm/status"
  front_3mm_status_topic: "/apollo/camera/front_3mm/status"
  back_3mm_status_topic: "/apollo/camera/back_3mm/status"
  location_point_init_topic: "/apollo/mcloud/location_point_init"
  imu_topic: "imu_raw"
  tracker_camera_topic: "/apollo/tracker/camera"
  control_topic: "/apollo/control"
}

bridge_remote_config: {
  remote_ip : "*************"
  remote_port : 8080
}

nanomsg_config: {
  remote_url : "tcp://*************:8084"
}

ad_version_config: {
  adsens_version_file: "/apollo/adsens_version"
  adplan_version_file: "/apollo/adplan_version"
}