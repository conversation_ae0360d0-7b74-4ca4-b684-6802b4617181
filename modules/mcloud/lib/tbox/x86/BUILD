load("//tools:cpplint.bzl", "cpplint")
load("//tools/proto:proto.bzl", "apollo_py_binary")
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_import")
load("//tools:apollo_package.bzl", "apollo_cc_binary", "apollo_cc_library", "apollo_package")


package(default_visibility = ["//visibility:public"])


cc_import(
    name = "aes_lib",
    static_library = "libaes_x86.a",
)

cc_import(
    name = "cjson_lib",
    shared_library = "libcjson_x86.so",
)

cc_import(
    name = "common_lib",
    shared_library = "libcommon_x86.so",
)

cc_import(
    name = "socket_lib",
    shared_library = "libsocket_x86.so",
)

# 然后创建一个组合库
apollo_cc_library(
    name = "tboxlib",
    deps = [
        ":aes_lib",
        ":cjson_lib",
        ":common_lib",
        ":socket_lib",
    ],
)

apollo_package()
cpplint()
