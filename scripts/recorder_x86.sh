#! /usr/bin/env bash

if [[ ! -d "data/bag" ]]; then
    mkdir -p "data/bag"
fi

source cyber/setup.bash
cd data/bag/

##delete mpi record files  --zheqiang.wu  20231129
# At present, MPI records packages for test, and this script is still used to record packages
# so in this script, delete the mpi's record files for save disk
# In the future, if only MPI is used to record packets,
# this script will not start and then the deletion action will not be executed
if [ -d "/apollo/data/bag/mpi" ]; then
    find /apollo/data/bag/mpi -type f -mmin +600 -delete
fi

# avoid repeated record process
process_num=$(ps -ef | grep cyber_recorder | grep -w record | wc -l)
if [ ${process_num} -gt 0 ]; then
    process_id=$(ps -ef | grep cyber_recorder | grep -w record | awk '{print $2}')
    kill -9 ${process_id}
    echo "killed process_num_id:"${process_id}
fi

nohup cyber_recorder record -i 60 -m 2048 \
    -c /apollo/canbus/chassis_tcp \
    -c /apollo/control \
    -c /apollo/localization/pose_tcp \
    -c /apollo/perception/obstacles_tcp \
    -c /apollo/perception/traffic_light_tcp \
    -c /apollo/mcloud/super_traffic_light \
    -c /apollo/planning \
    -c /apollo/prediction_tcp \
    -c /apollo/routing_request \
    -c /apollo/routing_response \
    -c /apollo/routing_result \
    -c /apollo/monitor/monitor_data_x86 \
    -c /apollo/monitor/monitor_data_aarch_tcp \
    -c /apollo/mcloud \
    -c /tf \
    -c /tf_static
