#!/usr/bin/env bash

DIR="/apollo"
source ${DIR}/cyber/setup.bash
source ${DIR}/scripts/apollo_base.sh

platform=$(uname -m)
launch_number=0
stop_flag=0

declare -A mainboard_map
function init_mainboard() {
    if [ $platform == "aarch64" ]; then
        mainboard_map["static_transform"]="mainboard -d ${DIR}/modules/transform/dag/static_transform.dag -p transform_sched -s CYBER_DEFAULT"
        mainboard_map["monitor"]="mainboard -d ${DIR}/modules/monitor/dag/monitor.dag -p monitor_sched -s CYBER_DEFAULT"
        mainboard_map["tcp_transport"]="mainboard -d ${DIR}/modules/tcp_transport/dag/tcp_transport.dag  -p tcp_transport_sched -s CYBER_DEFAULT"
        
        mainboard_map["bridge_receiver_robosense_imu"]="mainboard \
        -d ${DIR}/modules/bridge/dag/bridge_receiver_robosense_imu.dag \
        -p bridge_imu_sched -s CYBER_DEFAULT"

        mainboard_map["bridge_sender_robosense"]="mainboard \
        -d ${DIR}/modules/bridge/dag/bridge_sender_robosense.dag \
        -p bridge_chassis_sched -s CYBER_DEFAULT"

        mainboard_map["bridge_receiver_robosense"]="mainboard \
        -d ${DIR}/modules/bridge/dag/bridge_receiver_robosense.dag \
        -p bridge_localization_sched -s CYBER_DEFAULT"

        mainboard_map["robosense32_lidar_tracking"]="mainboard \
        -d ${DIR}/modules/drivers/lidar/robosense/dag/rshelios.dag \
        -d ${DIR}/modules/perception/pointcloud_preprocess/dag/robosense32_pointcloud_preprocess.dag \
        -d ${DIR}/modules/perception/lidar_detection/dag/robosense32_lidar_detection.dag \
        -d ${DIR}/modules/perception/lidar_tracking/dag/robosense32_lidar_tracking.dag \
        -p robosense_sched -s CYBER_DEFAULT"

        mainboard_map["mems_lidar_tracking"]="mainboard \
        -d ${DIR}/modules/drivers/lidar/zvision/dag/online_calibration/zvision_ml30sa1_multi_sensor.dag \
        -d ${DIR}/modules/drivers/lidar/zvision/dag/fusion.dag \
        -d ${DIR}/modules/perception/pointcloud_preprocess/dag/mems_pointcloud_preprocess.dag \
        -d ${DIR}/modules/perception/lidar_detection/dag/mems_lidar_detection.dag \
        -d ${DIR}/modules/perception/lidar_tracking/dag/mems_lidar_tracking.dag \
        -p mems_sched -s CYBER_DEFAULT"

        mainboard_map["radar_detection"]="mainboard \
        -d ${DIR}/modules/drivers/radar/udas_ultrasonic/dag/udas_ultrasonic.dag \
        -d ${DIR}/modules/drivers/radar/cubtek_radar/dag/cubtek_radar.dag \
        -d ${DIR}/modules/perception/ultrasonic_detection/dag/ultrasonic_radar.dag \
        -d ${DIR}/modules/perception/radar_detection/dag/radar_detection.dag \
        -p radar_sched -s CYBER_DEFAULT"

        mainboard_map["multi_sensor_fusion"]="mainboard \
        -d ${DIR}/modules/perception/msg_adapter/dag/msg_adapter.dag \
        -d ${DIR}/modules/perception/multi_sensor_fusion/dag/multi_sensor_fusion.dag \
        -p fusion_sched -s CYBER_DEFAULT"

        mainboard_map["prediction"]="mainboard \
        -d ${DIR}/modules/prediction/dag/prediction.dag \
        -p prediction_sched -s CYBER_DEFAULT"

        mainboard_map["traffic_light_perception"]="mainboard \
        -d ${DIR}/modules/drivers/camera/dag/camera_verdex_3_0.dag \
        -d ${DIR}/modules/perception/traffic_light_perception/dag/traffic_light_perception.dag \
        -p traffic_light_sched -s CYBER_DEFAULT"

        mainboard_map["canbus"]="mainboard -d ${DIR}/modules/canbus/dag/canbus.dag -p mainboard_default -s CYBER_DEFAULT"

    elif [ $platform == "x86_64" ]; then
        process="routing"
        launch_cmd="mainboard -d ${DIR}/modules/routing/dag/routing.dag -p mainboard_default -s CYBER_DEFAULT "
        mainboard_map[${process}]=${launch_cmd}

        process="planning"
        launch_cmd="mainboard -d ${DIR}/modules/planning/dag/planning.dag -p planning_sched -s CYBER_DEFAULT "
        mainboard_map[${process}]=${launch_cmd}

        process="control"
        launch_cmd="mainboard -d ${DIR}/modules/control/dag/control.dag -p mainboard_default -s CYBER_DEFAULT "
        mainboard_map[${process}]=${launch_cmd}

        process="mcloud_bridge"
        launch_cmd="mainboard -d ${DIR}/modules/mcloud/dag/mcloud_bridge.dag  -p mainboard_default -s CYBER_DEFAULT "
        mainboard_map[${process}]=${launch_cmd}

        process="monitor"
        launch_cmd="mainboard -d ${DIR}/modules/monitor/dag/monitor.dag -p monitor_sched -s CYBER_DEFAULT "
        mainboard_map[${process}]=${launch_cmd}

        mainboard_map["tcp_transport"]="mainboard -d ${DIR}/modules/tcp_transport/dag/tcp_transport.dag  -p tcp_transport_sched -s CYBER_DEFAULT"
        mainboard_map["mpi_checker"]="mainboard -d ${DIR}/modules/mpi/dag/mpi_checker.dag -p monitor_sched -s CYBER_DEFAULT"
        mainboard_map["mpi_collector"]="mainboard -d ${DIR}/modules/mpi/dag/mpi_collector.dag -p monitor_sched -s CYBER_DEFAULT"
    fi
    echo "number of mainboard process is ${#mainboard_map[@]}"
}

declare -A gt_map
function init_gnome_terminal() {
    if [ $platform == "aarch64" ]; then
        # gt_map["rs_perception_and_localization"]="bash ${DIR}/scripts/rs_perception_and_localization.sh"

        # ptp4l not a permanent process
        # gt_map["ptp4l_102"]="bash ${DIR}/scripts/ptp4l_102.sh; exec bash"

        # Self developed version does not start rs_sender
        # gt_map["rs_sender"]="bash ${DIR}/scripts/rs_sender.sh; exec bash"

        # huifeimao saying it's not a resident thread
        # gt_map["set_camera_10_fps"]="bash ${DIR}/scripts/set_camera_10_fps.sh; exec bash"

        # canbus start using mainboard, so put it in the mainboard section
        # gt_map["sadf"]="bash ${DIR}/autoset.sh; exec bash"
        gt_map["recorder"]="bash /apollo/scripts/recorder_aarch.sh"
    elif [ $platform == "x86_64" ]; then
        gt_map["recorder"]="bash /apollo/scripts/recorder_x86.sh"
    fi
    echo "number of gnome_terminal process is ${#gt_map[@]}"
}

function process_alive() {
    local process1=$1
    local process2=$2
    local process_num=$(ps -ef | grep ${process1} | grep ${process2} | grep -v "grep" | wc -l)
    if [ ${process_num} -gt 0 ]; then
        return 1
    else
        echo "${process1} ${process2} not alive"
        return 0
    fi
}

function check_mainboard() {
    echo "############## check_mainboard start:$(date "+%Y-%m-%d %H:%M:%S")"

    for key in ${!mainboard_map[@]}; do
        process_alive "mainboard" "${key}.dag"
        local res=$(echo $?)
        if [ ${res} -eq 0 ]; then

            launch_number=$(($launch_number + 1))
            nohup ${mainboard_map[${key}]} >/dev/null 2>&1 &
            echo "launch process:${key} ,launch cmd:${mainboard_map[${key}]}"
        fi

    done
    echo "############## check_mainboard end:$(date "+%Y-%m-%d %H:%M:%S")"
}

function check_gnome_terminal() {
    echo "############## check_gnome_terminal start:$(date "+%Y-%m-%d %H:%M:%S")"

    for key in ${!gt_map[@]}; do
        process_alive "bash" ${key}
        local res=$(echo $?)
        if [ ${res} -eq 0 ]; then
            launch_number=$(($launch_number + 1))
            nohup ${gt_map[${key}]} >/dev/null 2>&1 &
            echo "launch process:${key} ,launch cmd:${gt_map[${key}]}"
        fi

    done
    echo "############## check_gnome_terminal end:$(date "+%Y-%m-%d %H:%M:%S")"
}

function main() {

    cd ${DIR}
    log_file="${DIR}/data/log/node_daemon.log"
    init_mainboard >>${log_file}
    init_gnome_terminal >>${log_file}

    # wait all node already been pulled up by launch_system.sh
    sleep 40

    while true; do
        launch_number=0
        check_mainboard >>${log_file}
        check_gnome_terminal >>${log_file}

        if [ ${launch_number} -gt 0 ]; then
            echo "launch_number=${launch_number}" >>${log_file}
            sleep 1
        else
            sleep 15
        fi

        if [ ${stop_flag} -gt 0 ]; then
            break
        fi
    done
}

main
