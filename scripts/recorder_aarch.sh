#! /usr/bin/env bash

if [[ ! -d "data/bag" ]]; then
    mkdir -p "data/bag"
fi

source cyber/setup.bash
cd data/bag/

##delete mpi record files  --zheqiang.wu  20231129
# At present, MPI records packages for test, and this script is still used to record packages
# so in this script, delete the mpi's record files for save disk
# In the future, if only MPI is used to record packets,
# this script will not start and then the deletion action will not be executed
if [ -d "/apollo/data/bag/mpi" ]; then
    find /apollo/data/bag/mpi -type f -mmin +600 -delete
fi

# avoid repeated record process
process_num=$(ps -ef | grep cyber_recorder | grep -w record | wc -l)
if [ ${process_num} -gt 0 ]; then
    process_id=$(ps -ef | grep cyber_recorder | grep -w record | awk '{print $2}')
    kill -9 ${process_id}
    echo "killed process_num_id:"${process_id}
fi

nohup cyber_recorder record -i 60 -m 2048 \
    -c /apollo/canbus/chassis \
    -c /apollo/control_tcp \
    -c /apollo/localization/pose \
    -c /apollo/perception/obstacles \
    -c /apollo/perception/traffic_light \
    -c /apollo/mcloud/super_traffic_light_tcp \
    -c /apollo/planning_tcp \
    -c /apollo/prediction \
    -c /apollo/tracker/rs \
    -c /apollo/tracker/mems \
    -c /apollo/tracker/camera \
    -c /apollo/tracker/camera_front_3mm \
    -c /apollo/camera/front_12mm/status \
    -c /apollo/camera/front_3mm/status \
    -c /apollo/tracker/radar \
    -c /apollo/tracker/ultrasonic \
    -c /apollo/sensor/udas_ultrasonic \
    -c /apollo/sensor/radar \
    -c /apollo/monitor/monitor_data_x86_tcp \
    -c /apollo/monitor/monitor_data_aarch \
    -c /apollo/mcloud_tcp \
    -c imu_raw \
    -c /tf \
    -c /tf_static
